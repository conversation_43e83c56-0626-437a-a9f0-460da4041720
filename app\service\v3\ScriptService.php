<?php

namespace app\service\v3;

use app\BaseService;
use app\model\Script;
use app\validate\ScriptValidate;
use think\facade\Db;

/**
 * 脚本
 * Class ScriptService
 * @package app\service\v3
 */
class ScriptService extends BaseService
{
    public function __construct()
    {
        $this->model       = new Script;
        $this->validate    = ScriptValidate::class;
        $this->select_with = [];
    }

    public function getTrOrders($param)
    {
        $desc = "获取三方平台订单";

//        $start_time = "2025-06-01 00:00:00";
//        $end_time   = "2025-06-30 23:59:59";

        $data[] = "";

        $orders = (new XiaoHongshu())->getOrders();
//        $orders = (new TaoBao())->getOrders($start_time, $end_time);

        print_r($orders);
        die;

        print_r(outExcel($data, $desc));
        die;
    }


    public function getTrOrdersbak($param)
    {
        $where = [
            ['order_id', 'in', array(
                "P757516738787055251",
                "P758209817049231261",
                "P757516052081375111",
                "P757519082564218751",
                "P757561512715317271",
                "P757510535177509371"
            )]
        ];

        // 查询数据
        $list          = Db::connect('database_orders_prod')->name('sales_data')
            ->where($where)
            ->order('created_at', 'desc')
            ->column('month,platform_type,corp,platform_name,main_order_id,order_id,order_status,order_amount,merchant_receivable,merchant_receivable,receivable_amount,shipping_time,payment_channel');
        $sub_order_nos = array_column($list, 'order_id');
        #region  回款数据 settlement
        $settlements      = [];
        $temp_settlements = Db::connect('database_orders_dev')->name('tripartite_settlement')
            ->where('sub_order_no', 'in', $sub_order_nos)
            ->where('business', 'in', ['退款', '收入'])//'退款','收入'
            ->where('status', '=', 2)//已审核
            ->order('create_time', 'desc')
            ->column('sub_order_no,amount,business,create_time');

        foreach ($temp_settlements as $item) {
            if (empty($settlements[$item['sub_order_no']])) {
                $tmp_settlement = [
                    'refund_amount'   => 0, //退款金额
                    'received_amount' => 0, //已回款金额
                    'payment_date'    => ($item['create_time'] > 0) ? date('Y-m-d', $item['create_time']) : "", //回款日期
                ];
            } else {
                $tmp_settlement = $settlements[$item['sub_order_no']];
            }
            if (in_array($item['business'], ['退款'])) {
                $tmp_settlement['refund_amount'] = bcadd($tmp_settlement['refund_amount'], $item['amount'], 2);
            }
            if (in_array($item['business'], ['收入'])) {
                $tmp_settlement['received_amount'] = bcadd($tmp_settlement['received_amount'], $item['amount'], 2);
            }
            $settlements[$item['sub_order_no']] = $tmp_settlement;
        }
        unset($temp_settlements);
        #endregion $settlements
        #region ERP销售数据 $erp_datas
        $erp_datas         = [];
        $sales_order_dates = Db::connect('database_orders_prod')->name('tripartite_order')
            ->where('sub_order_no', 'in', $sub_order_nos)
            ->where('delivery_time', '>', 0)
            ->column('delivery_time', 'sub_order_no');
        $sales_order_dates = array_merge($sales_order_dates, Db::connect('database_orders_prod')->name('offline_order')
            ->where('sub_order_no', 'in', $sub_order_nos)
            ->where('delivery_time', '>', 0)
            ->column('delivery_time', 'sub_order_no'));
        $erp_amounts       = \Curl::erpGetAmounts(['sub_order_no' => implode(',', $sub_order_nos)]);

        foreach ($sub_order_nos as $sub_order_no) {
            $erp_datas[$sub_order_no]                      = $erp_amounts[$sub_order_no] ?? [
                'u8c_029_amount'    => 0,
                'u8c_515_amount'    => 0,
                't_plus_002_amount' => 0,
                't_plus_008_amount' => 0,
            ];
            $erp_datas[$sub_order_no]['sales_order_total'] = round(array_sum(array_values($erp_datas[$sub_order_no])), 2);
            $erp_datas[$sub_order_no]['sales_order_date']  = !empty($sales_order_dates[$sub_order_no]) ? date('Y-m-d', $sales_order_dates[$sub_order_no]) : '';
        }
        unset($erp_amounts, $sales_order_dates);
        #endregion ERP销售数据 $erp_datas

        foreach ($list as &$order) {
            $settlement                = $settlements[$order['order_id']] ?? [
                'refund_amount'   => 0,
                'received_amount' => 0,
                'payment_date'    => ''
            ];
            $erp_data                  = $erp_datas[$order['order_id']] ?? [
                'u8c_029_amount'    => 0,
                'u8c_515_amount'    => 0,
                't_plus_002_amount' => 0,
                't_plus_008_amount' => 0,
                'sales_order_total' => 0,
                'sales_order_date'  => '',
            ];
            $order                     = array_merge($order, $settlement, $erp_data);
            $order['pending_payment']  = bcsub($order['receivable_amount'], $order['received_amount'], 2); // 待回款
            $order['unshipped_amount'] = bcsub($order['receivable_amount'], $order['sales_order_total'], 2); // 本月未发货金额
            $order['shipping_time']    = ($order['shipping_time'] > 0) ? date('Y-m-d', $order['shipping_time']) : ""; //回款日期
        }

        return $this->success([
            'list' => $list,
        ]);
    }

    public function receivedAmountAllocation($param)
    {
        $desc = "三方订单金额回款金额分配";

//        $db = Db::connect('database_orders_prod');
        $db = Db::connect('database_orders_dev');

        try {
            // 开启事务
            $db->startTrans();

            // 1. 查询三方结算单数据
            $settlements = $db->name('tripartite_settlement')
                ->where('wait_amount', '>', 0)
                ->where('status', '=', 2)
                ->where('business', '=', '收入')
                ->order('create_time', 'desc')
                ->select()
                ->toArray();

            if (empty($settlements)) {
                echo "没有找到符合条件的结算单数据\n";
                return $this->success(['message' => '没有找到符合条件的结算单数据']);
            }

            // 按主订单分组结算单数据
            $settlementsByMainOrder = [];
            foreach ($settlements as $settlement) {
                $mainOrderNo = $settlement['main_order_no'];
                if (!isset($settlementsByMainOrder[$mainOrderNo])) {
                    $settlementsByMainOrder[$mainOrderNo] = [];
                }
                $settlementsByMainOrder[$mainOrderNo][] = $settlement;
            }

            $mainOrderNos = array_keys($settlementsByMainOrder);
            echo "找到主订单数量: " . count($mainOrderNos) . "\n";

            // 2. 查询销售数据
            $salesData = $db->name('sales_data')
                ->where('main_order_id', 'in', $mainOrderNos)
                ->whereRaw('(receivable_amount - received_amount) > 0')
                ->order('end_time', 'desc')
                ->select()
                ->toArray();

            if (empty($salesData)) {
                echo "没有找到符合条件的销售数据\n";
                return $this->success(['message' => '没有找到符合条件的销售数据']);
            }

            // 按主订单分组销售数据
            $salesDataByMainOrder = [];
            foreach ($salesData as $sale) {
                $mainOrderId = $sale['main_order_id'];
                if (!isset($salesDataByMainOrder[$mainOrderId])) {
                    $salesDataByMainOrder[$mainOrderId] = [];
                }
                $salesDataByMainOrder[$mainOrderId][] = $sale;
            }

            echo "找到销售数据主订单数量: " . count($salesDataByMainOrder) . "\n";


            // 3. 准备批量更新的数据
            $salesUpdates      = [];        // 销售数据更新
            $settlementUpdates = [];   // 结算单更新
            $allocationRecords = [];   // 分配记录
            $totalAllocated    = 0;

            // 4. 开始分配金额计算（内存中处理）
            foreach ($settlementsByMainOrder as $mainOrderNo => $settlements) {
                if (!isset($salesDataByMainOrder[$mainOrderNo])) {
                    echo "主订单 {$mainOrderNo} 没有对应的销售数据，跳过\n";
                    continue;
                }

                $salesOrders = $salesDataByMainOrder[$mainOrderNo];
                echo "处理主订单: {$mainOrderNo}，结算单数量: " . count($settlements) . "，销售订单数量: " . count($salesOrders) . "\n";

                // 对每个结算单进行分配
                foreach ($settlements as $settlement) {
                    $waitAmount         = floatval($settlement['wait_amount']);
                    $settlementId       = $settlement['id'];
                    $maxCreateTime      = $settlement['create_time'];
                    $originalWaitAmount = $waitAmount;

                    echo "处理结算单ID: {$settlementId}，待分配金额: {$waitAmount}\n";

                    // 对该主订单下的销售订单进行分配
                    foreach ($salesOrders as &$salesOrder) {
                        if ($waitAmount <= 0) {
                            break; // 结算金额已分配完
                        }

                        $receivableAmount = floatval($salesOrder['receivable_amount']);
                        $receivedAmount   = floatval($salesOrder['received_amount']);
                        $needAmount       = $receivableAmount - $receivedAmount;

                        if ($needAmount <= 0) {
                            continue; // 该订单不需要分配
                        }

                        // 计算本次分配金额
                        $allocateAmount = min($waitAmount, $needAmount);

                        if ($allocateAmount > 0) {
                            $orderId           = $salesOrder['order_id'];
                            $newReceivedAmount = $receivedAmount + $allocateAmount;

                            // 准备销售数据更新
                            $salesUpdates[$orderId] = [
                                'received_amount' => $newReceivedAmount,
                                'payment_date'    => date('Y-m-d', $maxCreateTime),
                                'updated_at'      => date('Y-m-d H:i:s')
                            ];

                            // 准备分配记录
                            $allocationRecords[] = [
                                'settlement_id' => $settlementId,
                                'sub_order_no'  => $orderId,
                                'amount'        => $allocateAmount,
                                'create_time'   => time()
                            ];

                            // 更新待分配金额和本地数据
                            $waitAmount                    -= $allocateAmount;
                            $totalAllocated                += $allocateAmount;
                            $salesOrder['received_amount'] = $newReceivedAmount;

                            echo "计算分配 - 子订单: {$orderId}，分配金额: {$allocateAmount}，剩余待分配: {$waitAmount}\n";
                        }
                    }

                    // 准备结算单更新
                    if ($waitAmount != $originalWaitAmount) {
                        $settlementUpdates[$settlementId] = ['wait_amount' => $waitAmount];
                        echo "计算结算单更新ID: {$settlementId}，新的待分配金额: {$waitAmount}\n";
                    }
                }
            }

            echo "开始批量更新数据库...\n";

            // 5. 批量更新销售数据
            if (!empty($salesUpdates)) {
                echo "批量更新销售数据，数量: " . count($salesUpdates) . "\n";
                foreach ($salesUpdates as $orderId => $updateData) {
                    $db->name('sales_data')
                        ->where('order_id', $orderId)
                        ->update($updateData);
                }
            }

            // 6. 批量更新结算单数据
            if (!empty($settlementUpdates)) {
                echo "批量更新结算单数据，数量: " . count($settlementUpdates) . "\n";
                foreach ($settlementUpdates as $settlementId => $updateData) {
                    $db->name('tripartite_settlement')
                        ->where('id', $settlementId)
                        ->update($updateData);
                }
            }

            // 7. 批量插入分配记录
            if (!empty($allocationRecords)) {
                echo "批量插入分配记录，数量: " . count($allocationRecords) . "\n";
                // 分批插入，避免单次插入数据过多
                $batchSize = 1000;
                $batches   = array_chunk($allocationRecords, $batchSize);
                foreach ($batches as $batch) {
                    $db->name('sales_settlement')->insertAll($batch);
                }
            }

            // 提交事务
            $db->commit();

            echo "分配完成！\n";
            echo "总分配金额: {$totalAllocated}\n";
            echo "分配记录数量: " . count($allocationRecords) . "\n";

            return $this->success([
                'message'                  => '分配完成',
                'total_allocated'          => $totalAllocated,
                'allocation_count'         => count($allocationRecords),
                'sales_updates_count'      => count($salesUpdates),
                'settlement_updates_count' => count($settlementUpdates)
            ]);

        } catch (\Exception $e) {
            // 回滚事务
            $db->rollback();
            echo "分配过程中发生错误: " . $e->getMessage() . "\n";
            return $this->error('分配过程中发生错误: ' . $e->getMessage());
        }
    }

}



