<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

Route::rule('/', function () {
    return 'tp6-script';
});


Route::group("script", function () {
    Route::group("v3", function () {


        //微信公众号帖子
        Route::post("articles/embedding", "Articles/embedding"); //embedding数据库


        //商家秒发
        Route::post("vmall/redisServiceRadius", "Vmall/redisServiceRadius"); //刷新所有店铺缓存
        Route::post("vmall/htmlToMd", "Vmall/htmlToMd"); //三方HTML文档转MD
        Route::post("vmall/shopsLonglatsData", "Vmall/shopsLonglatsData"); //商家经纬度新表数据脚本
        Route::post("vmall/jfbsp", "Vmall/jfbsp"); //解放碑商品导出

        //公社
        Route::post("gs/exportUserData", "Gs/exportUserData"); //添加
        Route::post("gs/companyDataExport", "Gs/companyDataExport"); //导出数据: 公司数据导出
        Route::post("gs/adminsExport", "Gs/adminsExport"); //导出数据: 管理员导出

        //埋点
        Route::post("md/exportUserPhone", "Md/exportUserPhone"); //导出数据: 公司数据导出

        //商品数据同步
        Route::post("commodities/syncSecondData", "Commodities/syncSecondData"); //添加
        Route::post("commodities/syncSecondDataProd", "Commodities/syncSecondDataProd"); //添加
        Route::post("commodities/syncFlashData", "Commodities/syncFlashData"); //添加
        Route::post("commodities/syncFlashDataProd", "Commodities/syncFlashDataProd"); //添加
        Route::post("commodities/syncFlashDataProdBath", "Commodities/syncFlashDataProdBath"); //添加
        Route::post("commodities/exportFlashPeriods", "Commodities/exportFlashPeriods"); //刘歆韵 彭媛媛 为筛选条件，导出闪购全部期数及相关信息
        Route::post("commodities/xb", "Commodities/xb"); //香槟在售待售期数添加到专题活动
        Route::post("commodities/xbExport", "Commodities/xbExport"); //专题活动商品导出
        Route::post("commodities/periodsSecondMerchantsSync", "Commodities/periodsSecondMerchantsSync"); //同步秒发商品销量
        Route::post("commodities/productExport", "Commodities/productExport"); //简码详情导出
        Route::post("commodities/searchOptimize", "Commodities/searchOptimize"); //首页搜索优化
        Route::post("commodities/zbxl", "Commodities/zbxl"); //直播销量统计
        Route::post("commodities/vmallInventory", "Commodities/vmallInventory"); //商家秒发数据同步
        Route::post("commodities/flash20230901", "Commodities/flash20230901"); //2023年9月1日至今闪购上架的所有期数信息
        Route::post("commodities/qwlBuyer", "Commodities/qwlBuyer"); //导出强威龙所有的期数及供应商
        Route::post("commodities/supplierPeriods", "Commodities/supplierPeriods"); //供应商期数
        Route::post("commodities/batchPeriods", "Commodities/batchPeriods"); //批量修改期数
        Route::post("commodities/flashPeriodsExport", "Commodities/flashPeriodsExport"); //导出闪购非渠道商品

        //萌芽
        Route::post("wms/dataSeparation", "Wms/dataSeparation"); //添加
        Route::post("wms/exportSeparationData", "Wms/exportSeparationData"); //导出仓库数据
        Route::post("wms/exportSeparationDataCompany", "Wms/exportSeparationDataCompany"); //导出仓库数据
        Route::post("wms/userMatch", "Wms/userMatch"); //导出仓库数据
        Route::post("wms/exportRdCode", "Wms/exportRdCode"); //导出仓库数据

        //工单
        Route::post("work/waybillsKuaidi100", "Work/waybillsKuaidi100"); //JD快递100跑数据

        //营销活动
        Route::post("marketing/couponQueue", "Marketing/couponQueue"); //优惠券发放队列正式

        Route::post("marketing/activityTargetUser", "Marketing/activityTargetUser"); //优惠券发放队列正式

        //线下门店
        Route::post("store/aliConfigAdd", "Store/aliConfigAdd"); //添加

        //线下门店
        Route::post("orders/exportOfflineOrder", "orders/exportOfflineOrder"); //添加
        Route::post("orders/purchasedUser", "orders/purchasedUser"); //添加
        Route::post("orders/preSalesEfficiency", "orders/preSalesEfficiency"); //售前客服效率导出
        Route::post("orders/gzUser", "orders/gzUser"); //导出广州3年有消费非沉默用户手机号
        Route::post("orders/jxUser", "orders/jxUser");
        Route::post("orders/jxUser202311", "orders/jxUser202311");
        Route::post("orders/shUser", "orders/shUser");
        Route::post("orders/ssd", "orders/ssd");//首三单第三单
        Route::post("orders/auctionTransfer", "orders/auctionTransfer");//首三单第三单
        Route::post("orders/leftoverUser", "orders/leftoverUser");
        Route::post("orders/shTop50User", "orders/shTop50User");
        Route::post("orders/createD", "orders/createD");
        Route::post("orders/crossDelayed", "orders/crossDelayed"); //跨境批量延期发货
        Route::post("orders/liejiu", "orders/liejiu"); //最近一个月购买烈酒
        Route::post("orders/clearTable", "orders/clearTable"); //清理数据库
        Route::post("orders/jx2024", "orders/jx2024"); //售前客服绩效 2024年导出
        Route::post("orders/bjsp2023", "orders/bjsp2023"); //白酒视频2023年销售数据导出
        Route::post("orders/sfddfh", "orders/sfddfh"); //更新三方订单发货状态,发货单号
        Route::post("orders/djdd", "orders/djdd"); //导出所有已支付的【定金订单】
        Route::post("orders/kjtk", "orders/kjtk"); //跨境退款订单
        Route::post("orders/smzdmErrOrders", "orders/smzdmErrOrders"); //什么值得买错误规则
        Route::post("orders/listOutExcel", "orders/listOutExcel"); //列表数据导出
        Route::post("orders/getSalesNums", "orders/getSalesNums"); //列表数据导出
        Route::post("orders/sales2023", "orders/sales2023"); //销售数据2023
        Route::post("orders/crossSales", "orders/crossSales"); //导出跨境在售所有商品期数
        Route::post("orders/sytjsj", "orders/sytjsj"); //私域统计数据
        Route::post("orders/msj", "orders/msj"); //马士基专题活动订单数据
        Route::post("orders/orderslist2023", "orders/orderslist2023"); //有支付时间且下单时间在23年的所有酒云网订单
        Route::post("orders/ptjsales2023", "orders/ptjsales2023"); //葡萄酒期数销售数据详情
        Route::post("orders/crossPushOut", "orders/crossPushOut"); //跨境推单数据导出
        Route::post("orders/bj20230912", "orders/bj20230912"); //23年白酒9至12月的订单明细
        Route::post("orders/qczbjd", "orders/qczbjd"); //清仓直播酒单
        Route::post("orders/salesData", "orders/salesData"); //销售数据
        Route::post("orders/takeIn", "orders/takeIn"); //销售数据
        Route::post("orders/earnest", "orders/earnest"); //拍卖未退款保证金
        Route::post("orders/rossTsList", "orders/rossTsList"); //跨境暂存退款订单
        Route::post("orders/stringFormat", "orders/stringFormat"); //多行文本格式化
        Route::post("orders/dateFormat", "orders/dateFormat"); //多行文本格式化
        Route::post("orders/djddExport", "orders/djddExport"); //根据尾款订单号导出关联的【定金订单】
        Route::post("orders/tmgjkj", "orders/tmgjkj"); //天猫国际科技同步价格
        Route::post("orders/vhAdmins", "orders/vhAdmins"); //天猫国际科技同步价格
        Route::post("orders/expressBlocked", "orders/expressBlocked"); //快递受阻订单手机号
        Route::post("orders/crossAutoPush", "orders/crossAutoPush"); //跨境自动推单
        Route::post("orders/customsDirectPush", "orders/customsDirectPush"); //跨境直接推单
        Route::post("orders/cardGoods", "orders/cardGoods"); //卡片商品数据统计
        Route::post("orders/smzdmOrders", "orders/smzdmOrders"); //什么值得买订单导出
        Route::post("orders/crossTSRefund", "orders/crossTSRefund"); //暂存后成功退款的订单数量及明细;
        Route::post("orders/crossStorage", "orders/crossStorage"); //跨境现货库存表;
        Route::post("orders/supplychainExport", "orders/supplychainExport"); //供应链-往来单位数据导出;
        Route::post("orders/smzdmLrl", "orders/smzdmLrl"); //什么值得买利润率;
        Route::post("orders/crossOrdersPhone", "orders/crossOrdersPhone"); //跨境购买记录用户手机号导出;
        Route::post("orders/salesAfterRemarks", "orders/salesAfterRemarks"); //售后客服备注;
        Route::post("orders/warehouseOrders", "orders/warehouseOrders"); //推送仓库订单;
        Route::post("orders/tsOrders", "orders/tsOrders"); //暂存订单;
        Route::post("orders/smzdmOrderStatus", "orders/smzdmOrderStatus"); //什么值得买订单状态;
        Route::post("orders/barchTsOrders", "orders/barchTsOrders"); //批量取消订单暂存;
        Route::post("orders/barchPushWms", "orders/barchPushWms"); //批量推送萌芽;
        Route::post("orders/allOrdersPhone", "orders/allOrdersPhone"); //购买记录用户手机号导出;
        Route::post("orders/crossMonitoring", "orders/crossMonitoring"); //跨境违规监控;
        Route::post("orders/labelExport", "orders/labelExport"); //标签数据导入;
        Route::post("orders/crossAddressList", "orders/crossAddressList"); //crossAddressList;
        Route::post("orders/crossExport", "orders/crossExport"); //跨境收货地址导入到相似度查询;
        Route::post("orders/crossMoveOffExport", "orders/crossMoveOffExport"); //导出跨境近2个月没有动销的产品ID;
        Route::post("orders/similarFind", "orders/similarFind"); //相似度查询;
        Route::post("orders/pushAddressToAli", "orders/pushAddressToAli"); //上传向量到阿里数据库;
        Route::post("orders/abnormalOrder", "orders/abnormalOrder"); //141761期酒款异常订单;
        Route::post("orders/preDelivery", "orders/preDelivery"); //提前发货订单信息导出;
        Route::post("orders/liquorSoldNum", "orders/liquorSoldNum"); //23年酒类销售瓶数;
        Route::post("orders/crossTSRefundOrders", "orders/crossTSRefundOrders"); //23年酒类销售瓶数;
        Route::post("orders/buyer", "orders/buyer"); //导出24年1月1日-至今购买过秒发的用户数据;
        Route::post("orders/crossLimit", "orders/crossLimit"); //跨境额度;
        Route::post("orders/batchCancellation", "orders/batchCancellation"); //批量取消;
        Route::post("orders/batchLottery", "orders/batchLottery"); //批量增加抽奖次数;
        Route::post("orders/batchRemoval", "orders/batchRemoval"); //批量下架期数;
        Route::post("orders/bulkCoupon", "orders/bulkCoupon"); //批量发放优惠券;
        Route::post("orders/bulkCouponByExcel", "orders/bulkCouponByExcel"); //批量发放优惠券根据Excel;
        Route::post("orders/bulkCouponByPeriod", "orders/bulkCouponByPeriod"); //期数退款批量发放优惠券
        Route::post("orders/trOrders", "orders/trOrders"); //三方订单收货人是否注册酒云;
        Route::post("orders/changeOrders", "orders/changeOrders"); //批量更新订单;
        Route::post("orders/encryptIds", "orders/encryptIds"); //渠道加密ID;
        Route::post("orders/changeTrOrders", "orders/changeTrOrders"); //批量更新三方订单;
        Route::post("orders/changeCrossOrders", "orders/changeCrossOrders"); //批量更新跨境订单;
        Route::post("orders/crossExpOrdersExport", "orders/crossExpOrdersExport"); //跨境订单快递公司导出;
        Route::post("orders/periodNewUsers", "orders/periodNewUsers"); //期数新购用户数;
        Route::post("orders/reorder", "orders/reorder"); //跨境重推;
        Route::post("orders/periodOrders", "orders/periodOrders"); //期数订单;
        Route::post("orders/qj202201PeriodsExport", "orders/qj202201PeriodsExport"); //2022年1月至今上架的所有清酒（非渠道）;
        Route::post("orders/supplychainData", "orders/supplychainData"); //客户数据同步
        Route::post("orders/crossUsers", "orders/crossUsers"); //客户数据同步
        Route::post("orders/appendFields", "orders/appendFields"); //追加字段
        Route::post("orders/supplierProducts", "orders/supplierProducts"); //供应商在售产品列表导出
        Route::post("orders/exportProducts", "orders/exportProducts"); //导出产品信息
        Route::post("orders/supplychainAddress", "orders/supplychainAddress"); //客户地址数据同步
        Route::post("orders/syncCustomerPriceBook", "orders/syncCustomerPriceBook"); //同步客户价格本
        Route::post("orders/exportPeriods", "orders/exportPeriods"); //导出期数
        Route::post("orders/exhibProductSync", "orders/exhibProductSync"); //展会产品同步价格
        Route::post("orders/supplychainUser", "orders/supplychainUser"); //供应链用户更新
        Route::post("orders/userOrdersExport", "orders/userOrdersExport"); //用户订单导出
        Route::post("orders/lyfcustomers", "orders/lyfcustomers"); //吕怡璠管理的往来单位
        Route::post("orders/crossOrders", "orders/crossOrders"); //需导出酒云网2023.1-2024.7月跨境销售订单数据，用于跨境动销分析
        Route::post("orders/appendPeriodsFields", "orders/appendPeriodsFields"); //追加期数字段
        Route::post("orders/usersPhone", "orders/usersPhone"); //购买记录用户手机号导出;
        Route::post("orders/consumptionGen1000usersPhone", "orders/consumptionGen1000usersPhone"); //消费总金额1000以上，但今年没下过单的用户;
        Route::post("orders/purchaseList", "orders/purchaseList"); //供应商交接 数据导出
        Route::post("orders/customerPriceExport", "orders/customerPriceExport"); //供应商交接 数据导出
        Route::post("orders/crossPeriodOrdersChange", "orders/crossPeriodOrdersChange"); //跨境期数, 订单更新
        Route::post("orders/movingPinStatistics", "orders/movingPinStatistics"); //市场部动销表-数据统计
        Route::post("orders/partnerEntityExport", "orders/partnerEntityExport"); //往来单位导出
        Route::post("orders/partnerEntitySync", "orders/partnerEntitySync"); //往来单位同步数据
        Route::post("orders/msj2023Export", "orders/msj2023Export"); //马士基
        Route::post("orders/sjbz", "orders/sjbz"); //杨文科说数据不准 排查下
        Route::post("orders/moreHeadersExport", "orders/moreHeadersExport"); //多级动态表头导出
        Route::post("orders/salesReturnExport", "orders/salesReturnExport"); //销售退货导出
        Route::post("orders/crossPeriodsUpdate", "orders/crossPeriodsUpdate"); //跨境期数更新
        Route::post("orders/supplierPeriodsOrders", "orders/supplierPeriodsOrders"); //供应商期数订单销售额
        Route::post("orders/supplychainConnect", "orders/supplychainConnect"); //供应商交接
        Route::post("orders/tripartiteOrderExport", "orders/tripartiteOrderExport"); //导出三方订单数据
        Route::post("orders/appendCrossFields", "orders/appendCrossFields"); //跨境订单追加字段
        Route::post("orders/tripartiteOrderPushWms", "orders/tripartiteOrderPushWms"); //三方订单推送萌芽
        Route::post("orders/tripartiteOrderUpdateWms", "orders/tripartiteOrderUpdateWms"); //三方订单推更新萌芽地址
        Route::post("orders/crossTsAutoPush", "orders/crossTsAutoPush"); //跨境取消暂存自动推单
        Route::post("orders/lwUserExport", "orders/lwUserExport"); //老外用户导出
        Route::post("orders/whitelist", "orders/whitelist"); //白名单
        Route::post("orders/exportOfflineOrders", "orders/exportOfflineOrders"); //导出线下订单
        Route::post("orders/exportOrderUser", "orders/exportOrderUser"); //导出订单用户信息
        Route::post("orders/exportJztOrder", "orders/exportJztOrder"); //导出酒展通订单
        Route::post("orders/salesOrdersExport", "orders/salesOrdersExport"); //销售数据导出
        Route::post("orders/channel2024", "orders/channel2024"); //2024年每个渠道拉新的用户数据
        Route::post("orders/lxqk2024", "orders/lxqk2024"); //2024年拉新情况
        Route::post("orders/inventoryProductExport", "orders/inventoryProductExport"); //EVA 库存产品导出
        Route::post("orders/jwsync", "orders/jwsync"); //酒闻同步
        Route::post("orders/crossPeriodChangeByExcel", "orders/crossPeriodChangeByExcel"); //酒闻同步
        Route::post("orders/allPeriodsExport", "orders/allPeriodsExport"); //导出所有期数
        Route::post("orders/partnerEntityUserSync", "orders/partnerEntityUserSync"); //往来单位用户数据同步
        Route::post("orders/fpExport", "orders/fpExport"); //发票导出
        Route::post("orders/periodExport", "orders/periodExport"); //期数导出
        Route::post("orders/appendProductFields", "orders/appendProductFields"); //商品导出
        Route::post("orders/userExport", "orders/userExport"); //用户数据统计
        Route::post("orders/orderExport", "orders/orderExport"); //订单数据导出
        Route::post("orders/ldAllProducts", "orders/ldAllProducts"); //全部产品数据导出
        Route::post("orders/listOrdersExcel", "orders/listOrdersExcel"); //订单列表数据
        Route::post("orders/mldckOrders", "orders/mldckOrders"); //木兰朵仓库订单
        Route::post("orders/jmzxOrders", "orders/jmzxOrders"); //简码对应最新订单
        Route::post("orders/changeCrossOrdersByExcel", "orders/changeCrossOrdersByExcel"); //修改跨境订单
        Route::post("orders/jmzxOrdersByExcel", "orders/jmzxOrdersByExcel"); //简码对应最新订单空白补缺
        Route::post("orders/b2bkhzlExport", "orders/b2bkhzlExport"); //b2b客户资料导入
        Route::post("orders/sjgscl", "orders/sjgscl"); //时间格式处理
        Route::post("orders/sfdddr", "orders/sfdddr"); //时间格式处理
        Route::post("orders/wxfgdjztyh", "orders/wxfgdjztyh"); //未消费过的酒展通用户
        Route::post("orders/snxssj", "orders/snxssj"); //广州深圳销售数据统计
        Route::post("orders/jhdddc", "orders/jhdddc"); //酒会订单导出
        Route::post("orders/q1xltj", "orders/q1xltj"); //Q1销量统计
        Route::post("orders/partnerEntityBmSync", "orders/partnerEntityBmSync"); //往来单位部门业务员数据同步
        Route::post("orders/zgptj", "orders/zgptj"); //中国专区葡萄酒销量统计
        Route::post("orders/czyqygys", "orders/czyqygys"); //C组已签约供应商
        Route::post("orders/kjfj", "orders/kjfj"); //跨境放假
        Route::post("orders/cgdmx", "orders/cgdmx"); //采购单明细导出
        Route::post("orders/offlineOrdersUpdate", "orders/offlineOrdersUpdate"); //线下订单更新
        Route::post("orders/yhysjdddc", "orders/yhysjdddc"); //一花一世界订单导出
        Route::post("orders/batchOrderUpdate", "orders/batchOrderUpdate"); //一花一世界订单导出
        Route::post("orders/channelOrdersExport", "orders/channelOrdersExport"); //渠道订单导出
        Route::post("orders/crossProductExport", "orders/crossProductExport"); //跨境产品销售数据导出
        Route::post("orders/ermsjzh", "orders/ermsjzh"); //二维码数据找回
        Route::post("orders/kizckzwh", "orders/kizckzwh"); //跨境暂存次品库存维护
        Route::post("orders/entitysUpdate", "orders/entitysUpdate"); //合作伙伴收款设置
        Route::post("orders/userAmountExport", "orders/userAmountExport"); //用户消费金额

//        $productId   = array_values(array_unique(call_user_func_array('array_merge', $productIdArr)));

        //埋点
        Route::post("md/add", "Md/add"); //添加                        
        Route::post("md/edit", "Md/edit"); //编辑
        Route::get("md/list", "Md/list"); //列表
        Route::get("md/detail", "Md/detail"); //详情
        Route::post("md/del", "Md/del"); //删除

        //拍卖
        Route::post("auction/encryptUser", "Auction/encryptUser"); //添加
        Route::post("auction/wmsCancelOrder", "Auction/wmsCancelOrder"); //添加


        //脚本
        Route::post("script/getTrOrders", "Script/getTrOrders"); //获取三方平台订单
        Route::post("script/receivedAmountAllocation", "Script/receivedAmountAllocation"); //三方订单金额回款金额分配

        ##########tp6-anchor##########
    })->prefix("v3.");
});

