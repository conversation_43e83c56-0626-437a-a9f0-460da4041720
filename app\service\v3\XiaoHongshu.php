<?php

namespace app\service\v3;

use app\BaseService;
use Exception;

/**
 * 脚本
 * Class ScriptService
 * @package app\service\v3
 */
class XiaoHongshu extends BaseService
{
    const          API_URL          = "https://ark.xiaohongshu.com/ark/open_api/v3/common_controller";
    const          API_VERSION      = "2.0";
    const          APP              = [
        # 第一个应用凭证
        'app1' => [
            'app_id'     => 'e840e096a975478984d4',
            'app_secret' => 'c7931a95f48312d7918cde5de2c47b8e'
        ],
        # 第二个应用凭证
        'app2' => [
            'app_id'     => '4787a2382e784ccb9b09',
            'app_secret' => '5a98d1879df35434299a7b7c62e9cb2b'
        ]
    ];
    const          SHOPS            = [
        [
            "platform_type"   => "小红书",
            "platform_name"   => "行吟信息科技（武汉）有限公司(云酒网小红书店）",
            "shop_id"         => "62b98b750d601800010dc853",
            "payment_channel" => '行吟小红书',
            'app'             => self::APP['app1'],
        ],
        [
            "platform_type"   => "小红书",
            "platform_name"   => "木兰朵-小红书",
            "shop_id"         => "650a60e17fa15200013acf16",
            "payment_channel" => '',
            'app'             => self::APP['app1'],
        ],
        [
            "platform_type"   => "小红书",
            "platform_name"   => "小红书-威哥蒸馏所",
            "shop_id"         => "65113b63effd830001ca90e0",
            "payment_channel" => '小红书 - 威哥蒸馏所',
            'app'             => self::APP['app2'],
        ],
        [
            "platform_type"   => "小红书",
            "platform_name"   => "小红书-Brown Brothers布琅兄弟",
            "shop_id"         => "653b599dbffe730001559bd6",
            "payment_channel" => '小红书-Brown Brothers布琅兄弟',
            'app'             => self::APP['app2'],
        ],
    ];
    const          ORDER_STATUS_MAP = [
        1  => "已取消", // 待支付,
        9  => "已取消", // 已取消
        2  => "已支付", // 已支付
        3  => "已支付", // 清关中
        4  => "已支付", // 待发货
        5  => "已发货", // 部分发货
        6  => "已发货", // 待收货
        7  => "已完成", // 已完成
        8  => "已完成", // 已关闭
        10 => "已完成", // 换货申请中
    ];

    protected $access_token = [];
    protected $shop_info    = [];

    public function __construct()
    {
    }

    private function getAccessToken($shop_id)
    {
        if (empty($this->access_token[$shop_id])) {
            $shop_info                    = \Curl::getXiaoHongShuShopInfo(compact('shop_id'));
            $this->access_token[$shop_id] = $shop_info['access_token'];
            $this->shop_info[$shop_id]    = $shop_info;
        }
        return $this->access_token[$shop_id];
    }

    private function callApi($shop, $method, $bizParams)
    {
        $timestamp           = strval(time());
        $requestBody         = array_merge([
            'appId'     => $shop['app']['app_id'],
            'timestamp' => $timestamp,
            'version'   => self::API_VERSION,
            'method'    => $method,
        ], $bizParams);
        $sign                = $this->calculateSign($method, $shop['app']['app_id'], $timestamp, self::API_VERSION, $shop['app']['app_secret']);
        $requestBody['sign'] = $sign;
        if (!empty($shop['access_token'])) {
            $requestBody['accessToken'] = $shop['access_token'];
        }
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, self::API_URL);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestBody));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json;charset=utf-8']);
        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new Exception('API请求失败: ' . curl_error($ch));
        }
        curl_close($ch);
        $data = json_decode($result, true);
        if (($data['error_code'] ?? 0) !== 0) {
            throw new Exception('API错误: ' . ($data['error_msg'] ?? '未知错误'));
        }
        return $data['data'] ?? [];
    }

    private function calculateSign($method, $appId, $timestamp, $version, $appSecret)
    {
        $paramStr = "$method?appId=$appId&timestamp=$timestamp&version=$version";
        $signStr  = $paramStr . $appSecret;
        return md5($signStr);
    }

    private function getShopOrders($shop, $start_time, $end_time)
    {
        print_r("获取{$shop['platform_name']}订单 $start_time - $start_time" . PHP_EOL);
        $time_windows = $this->getTimeWindow($start_time, $end_time, '+1 day -1 second');


        $all_order_nos = [];
        foreach ($time_windows as [$start, $end]) {
            $all_order_nos = array_merge($all_order_nos, $this->getOrderNos($shop, $start, $end));
        }


        $orders = [];
        foreach ($all_order_nos as $order_no) {
            $order = $this->getOrderDetail($shop, $order_no);
            if ($order) $orders[] = $order;
        }
        return $orders;
    }

    private function getOrderNos($shop, $startTime, $endTime)
    {
        $order_nos = [];
        $pageNo    = 1;
        $pageSize  = 100;
        do {
            $response  = $this->callApi($shop, 'order.getOrderList', [
                'startTime' => $startTime,
                'endTime'   => $endTime,
                'timeType'  => 1,
                'pageNo'    => $pageNo,
                'pageSize'  => $pageSize,
            ]);
            $orderList = $response['orderList'] ?? [];
            foreach ($orderList as $order) {
                $order_nos[] = $order['orderId'];
            }
            $total = $response['total'] ?? 0;
            $pageNo++;
        } while (count($order_nos) < $total);

        return $order_nos;
    }

    private function getOrderDetail($shop, $order_no)
    {
        $response = $this->callApi($shop, 'order.getOrderDetail', [
            'orderId' => $order_no,
        ]);
        return $this->convertToOrderObject($shop, $response);
    }

    private function convertToOrderObject($shop, $orderData)
    {
        $is_refund    = ($orderData['orderAfterSalesStatus'] == 3); # 售后状态大于1表示有售后
        $order_status = self::ORDER_STATUS_MAP[($orderData['orderStatus'] ?? 0)];
        if ($is_refund && in_array($orderData['orderStatus'], [7, 8, 10])) {
            $order_status = '已退款';
        }

//        $payment_amount   = round($orderData['totalPayAmount'] / 100.0, 2);
        $merchant_receive = round($orderData['merchantActualReceiveAmount'] / 100.0, 2); //商家实收金额
        if (in_array($order_status, ['已退款', '已取消'])) {
            $merchant_receivable = 0;
        } else {
            $merchant_receivable = $merchant_receive;
        }

        # 判断是否已发货
        return [
            'order_id'            => $orderData['orderId'] ?? '',
            'platform_type'       => $shop['platform_type'],
            'platform_name'       => $shop['platform_name'],
            'main_order_id'       => $orderData['orderId'] ?? '',
            'month'               => $this->convertTimestamp($orderData['createdTime'] ?? null, 'Y-m-d'),
            'order_status'        => $order_status,
            'shipping_time'       => $this->convertTimestamp($orderData['deliveryTime'] ?? null),
            'order_amount'        => $merchant_receive, //订单金额
            'merchant_receivable' => $merchant_receivable, //商家应收金额
            'receivable_amount'   => $merchant_receive, //应收金额
            'payment_channel'     => $shop['payment_channel'], //收款渠道
            'created_at'          => $this->convertTimestamp($orderData['createdTime'] ?? null),

//            'refund_amount' => 0, //退款金额
//            'u8c_029_amount' => 0, //U8C-029金额
//            'u8c_515_amount' => 0, //U8C-515金额
//            't_plus_002_amount' => 0, //T+002金额
//            't_plus_008_amount' => 0, //T+008金额
//            'sales_order_total' => 0, //销货单合计
//            'sales_order_date' => "", //销货单日期
//            'unshipped_amount' => 0, //本月未发货金额
//            'received_amount' => 0, //已回款金额
//            'payment_date' => "", //回款日期
//            'pending_payment' => 0, //回款日期
        ];
    }

    private function convertTimestamp($timestamp, $format = 'Y-m-d H:i:s')
    {
        if (!$timestamp) return null;
        return date($format, $timestamp / 1000);
    }


    public function getTimeWindow($start_time, $end_time, $interval = '+1 day')
    {
        $start_time   = new \DateTime($start_time);
        $end_time     = new \DateTime($end_time);
        $time_windows = [];

        $current = clone $start_time;
        while ($current < $end_time) {
            $window_end = clone $current;
            $window_end->modify($interval);


            if ($window_end > $end_time) $window_end = clone $end_time;

            $time_windows[] = [
                $current->getTimestamp(),
                $window_end->getTimestamp(),
            ];
            $current        = clone $window_end;
            $current->modify('+1 second'); //增加1秒 避免重复取值
        }

        return $time_windows;
    }

    public function getOrders($start_time, $end_time)
    {
        $orders = [];
        foreach (self::SHOPS as $shop) {
            $shop['access_token'] = $this->getAccessToken($shop['shop_id']);
            $orders               = array_merge($orders, $this->getShopOrders($shop, $start_time, $end_time));
        }
        return $orders;
    }
}


