<?php

namespace App\Service\V3;

use app\BaseService;
use DateTime;
use Exception;

/**
 * 淘宝订单获取器
 * 从Python代码转换而来的PHP版本
 */
class TaoBao extends BaseService
{
    // API基础URL
    const API_URL = "https://eco.taobao.com/router/rest";

    // 淘宝API版本
    const API_VERSION = "2.0";

    // 订单状态映射
    const ORDER_STATUS_MAP = [
        "WAIT_BUYER_PAY"           => "PENDING",             // 等待买家付款
        "TRADE_NO_CREATE_PAY"      => "PENDING",         // 没有创建支付宝交易
        "WAIT_SELLER_SEND_GOODS"   => "PROCESSING",   // 等待卖家发货,即:买家已付款
        "SELLER_CONSIGNED_PART"    => "SHIPPED",       // 卖家部分发货
        "WAIT_BUYER_CONFIRM_GOODS" => "SHIPPED",    // 等待买家确认收货,即:卖家已发货
        "TRADE_BUYER_SIGNED"       => "COMPLETED",        // 买家已签收,货到付款专用
        "TRADE_FINISHED"           => "COMPLETED",            // 交易成功
        "TRADE_CLOSED"             => "CLOSED",                 // 付款以后用户退款成功，交易自动关闭
        "TRADE_CLOSED_BY_TAOBAO"   => "CANCELLED",    // 付款以前，卖家或买家主动关闭交易
        "PAY_PENDING"              => "PENDING",                 // 国际信用卡支付付款确认中
        "PAID_FORBID_CONSIGN"      => "PROCESSING"       // 已付款但禁止发货
    ];

    // 淘宝应用凭证配置
    const TAOBAO_APP_CONFIGS = [
        // 第一个应用凭证
        'app1' => [
            'app_key'    => '32323720',
            'app_secret' => '450dd2a5a9a7d92b15a2c2c40d92e079'
        ]
    ];

    // 店铺与应用的映射关系
    const SHOP_APP_MAPPING = [
        // '店铺ID' => '应用配置键名'
        '227734657' => 'app1',  // 佰酿美酒天猫
        '419938814' => 'app1',  // 佰酿科技（天猫旗舰店）
        '566768114' => 'app1',  // 桃公子淘宝店
        '558695549' => 'app1',  // 天猫（法国南部葡萄酒官方旗舰店）
        '452890329' => 'app1',  // 酒遇喵掌柜
        '541276454' => 'app1',  // 美尼多天猫旗舰店
    ];

    private $platformShopConfig;
    private $shops;
    private $ordersManager;
    private $shopsInfo;
    private $orderQuery;
    private $maxRetries;
    private $retryDelay;

    public function __construct()
    {

        // 从PlatformShopConfig获取淘宝店铺配置
//        $this->platformShopConfig = new PlatformShopConfig();
//        $this->shops              = $this->platformShopConfig->getShopsByPlatform(PlatformType::TAOBAO);

        // 初始化订单管理器，用于保存订单到本地数据库
        $this->ordersManager = new OrdersManager(FolderManager::getDbFilePath());

        // 初始化所有店铺信息
        $this->shopsInfo  = $this->getAllShopsInfo();
        $this->orderQuery = new OrderQuery();  // 初始化ERP订单查询器

        // 设置API请求重试参数
        $this->maxRetries = 3;
        $this->retryDelay = 2;  // 秒

        // 确保订单处理记录表存在
        $this->ensureOrderProcessTableExists();

        echo "从配置中获取到 " . count($this->shopsInfo) . " 个淘宝店铺\n";
    }

    /**
     * 确保订单处理记录表存在
     */
    private function ensureOrderProcessTableExists()
    {
        $createTableSql = "
        CREATE TABLE IF NOT EXISTS order_process_records (
            order_id TEXT PRIMARY KEY,
            process_status INTEGER DEFAULT 0,  -- 0=待处理, 1=成功
            process_date TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ";

        try {
            $this->ordersManager->executeQuery($createTableSql);
            // 获取当前日期
            $todayDate = date('Y-m-d');

            // 删除非当日的订单处理记录
            $deleteOldRecordsSql = "
            DELETE FROM order_process_records
            WHERE process_date != ?
            ";

            try {
                $result = $this->ordersManager->executeQuery($deleteOldRecordsSql, [$todayDate]);
                echo "已清理非当日({$todayDate})的历史订单处理记录\n";
            } catch (Exception $e) {
                echo "清理历史订单处理记录失败: " . $e->getMessage() . "\n";
            }

            echo "订单处理记录表初始化成功\n";
        } catch (Exception $e) {
            echo "初始化订单处理记录表失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }

    /**
     * 将订单ID保存到处理表中
     */
    private function saveOrderIdsToProcessTable(array $orderIds, string $todayDate)
    {
        if (empty($orderIds)) {
            return;
        }

        try {
            // 查询已存在的订单记录
            $existingOrderIds = [];
            foreach ($orderIds as $orderId) {
                $query  = "SELECT order_id FROM order_process_records WHERE order_id = ?";
                $result = $this->ordersManager->executeQueryOne($query, [$orderId]);
                if ($result) {
                    $existingOrderIds[] = $result[0];
                }
            }

            // 需要插入的新订单ID
            $newOrderIds = array_diff($orderIds, $existingOrderIds);

            // 插入新订单记录
            if (!empty($newOrderIds)) {
                $insertQuery = "
                INSERT INTO order_process_records (order_id, process_status, process_date)
                VALUES (?, 0, ?)
                ";

                foreach ($newOrderIds as $orderId) {
                    try {
                        $this->ordersManager->executeQuery($insertQuery, [$orderId, $todayDate]);
                    } catch (Exception $e) {
                        echo "插入订单处理记录失败 ({$orderId}): " . $e->getMessage() . "\n";
                    }
                }

                echo "成功添加 " . count($newOrderIds) . " 个新订单到处理表\n";
            } else {
                echo "没有新订单需要添加到处理表\n";
            }

        } catch (Exception $e) {
            echo "保存订单ID到处理表失败: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 获取今日待处理的订单ID
     */
    private function getPendingOrderIds(array $orderIds, string $todayDate): array
    {
        if (empty($orderIds)) {
            return [];
        }

        $pendingOrderIds = [];

        try {
            foreach ($orderIds as $orderId) {
                $query  = "
                SELECT order_id FROM order_process_records
                WHERE order_id = ? AND process_date = ? AND process_status = 0
                ";
                $result = $this->ordersManager->executeQueryOne($query, [$orderId, $todayDate]);
                if ($result) {
                    $pendingOrderIds[] = $result[0];
                }
            }

            echo "找到 " . count($pendingOrderIds) . " 个待处理订单\n";
            return $pendingOrderIds;
        } catch (Exception $e) {
            echo "获取待处理订单ID失败: " . $e->getMessage() . "\n";
            return [];
        }
    }

    /**
     * 将订单标记为已处理
     */
    private function markOrderAsProcessed(string $orderId)
    {
        $updateQuery = "
        UPDATE order_process_records
        SET process_status = 1
        WHERE order_id = ?
        ";

        try {
            $this->ordersManager->executeQuery($updateQuery, [$orderId]);
            echo "订单 {$orderId} 已标记为处理成功\n";
        } catch (Exception $e) {
            echo "标记订单为已处理失败 ({$orderId}): " . $e->getMessage() . "\n";
        }
    }

    /**
     * 检查当日是否还有待处理的订单
     *
     * @param int $year 年份
     * @param int $month 月份
     * @return bool 是否有待处理订单
     */
    private function checkPendingOrders(int $year, int $month): bool
    {
        $todayDate = date('Y-m-d');

        // 检查订单处理表中是否有待处理的订单
        $query = "
        SELECT COUNT(*) FROM order_process_records
        WHERE process_date = ? AND process_status = 0
        ";

        try {
            $count        = $this->ordersManager->executeQueryOne($query, [$todayDate]);
            $pendingCount = $count ? $count[0] : 0;

            echo "当日还有 {$pendingCount} 个订单待处理\n";

            return $pendingCount > 0;

        } catch (Exception $e) {
            echo "检查待处理订单失败: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 获取所有淘宝店铺信息
     */
    private function getAllShopsInfo(): array
    {
        $shopsInfo = [];

        echo "正在获取 " . count($this->shops) . " 个淘宝店铺信息...\n";

        foreach ($this->shops as $shop) {
            $shopId   = $shop->getShopId();
            $shopName = $shop->getShopName();

            try {
                // 使用shop对象的getShopInfo方法获取店铺信息（包含access_token）
                $shopInfo = $shop->getShopInfo();
                if ($shopInfo === null) {
                    echo "警告: 店铺 {$shopName}({$shopId}) 信息获取失败，将跳过\n";
                    continue;
                }

                // 确定该店铺使用哪个应用配置
                $appKey    = self::SHOP_APP_MAPPING[$shopId] ?? 'app1';  // 默认使用app1
                $appConfig = self::TAOBAO_APP_CONFIGS[$appKey] ?? null;

                if (!$appConfig) {
                    echo "警告: 店铺 {$shopName}({$shopId}) 没有对应的应用配置，将跳过\n";
                    continue;
                }

                // 动态添加API调用所需的属性
                $shop->setAppKey($appConfig['app_key']);
                $shop->setAppSecret($appConfig['app_secret']);

                // 从shopInfo中获取access_token
                if (isset($shopInfo['access_token'])) {
                    $shop->setAccessToken($shopInfo['access_token']);
                } else {
                    echo "警告: 店铺 {$shopName}({$shopId}) 缺少access_token，将跳过\n";
                    continue;
                }

                $shopsInfo[$shopId] = $shopInfo;
                echo "成功获取店铺信息: {$shopName}({$shopId})，使用应用配置: {$appKey}\n";
            } catch (Exception $e) {
                echo "错误: 获取店铺 {$shopName}({$shopId}) 信息时出错: " . $e->getMessage() . "\n";
            }
        }

        if (empty($shopsInfo)) {
            throw new PlatformRunError("所有淘宝店铺信息获取失败");
        }

        return $shopsInfo;
    }

    /**
     * 获取指定年月的淘宝订单
     */
    public function getOrders(int $year, int $month): array
    {
        print_r($this->shops);die ;


//        $d = $this->getOrderDetail($shop)

        $this->validateDate($year, $month);

        if (empty($this->shopsInfo)) {
            throw new PlatformRunError("淘宝店铺信息未初始化");
        }

        $allOrders          = [];
        $maxFetchIterations = 5;  // 最大重试次数，避免无限循环

        for ($iteration = 0; $iteration < $maxFetchIterations; $iteration++) {
            if ($iteration > 0) {
                echo "第 " . ($iteration + 1) . " 次尝试获取待处理订单...\n";
            }

            $currentIterationOrders = [];

            // 对每个店铺获取订单
            foreach ($this->shops as $shop) {
                try {
                    // 检查是否有API凭证
                    if (!$shop->hasAppKey() || !$shop->hasAccessToken()) {
                        echo "跳过店铺 " . $shop->getShopName() . "，没有配置API凭证\n";
                        continue;
                    }

                    $shopOrders             = $this->fetchOrdersForMonth($shop, $year, $month);
                    $currentIterationOrders = array_merge($currentIterationOrders, $shopOrders);
                    echo "从淘宝店铺 " . $shop->getShopName() . " 获取到 " . count($shopOrders) . " 个订单\n";
                } catch (Exception $e) {
                    echo "获取淘宝店铺 " . $shop->getShopName() . " 订单失败: " . $e->getMessage() . "\n";
                }
            }

            $allOrders = array_merge($allOrders, $currentIterationOrders);

            // 检查是否还有待处理的订单
            $hasPendingOrders = $this->checkPendingOrders($year, $month);

            if (!$hasPendingOrders || empty($currentIterationOrders)) {
                // 如果没有待处理订单或者本次没有获取到订单，就退出循环
                break;
            }

            // 等待一段时间后继续下一轮获取
            if ($iteration < $maxFetchIterations - 1 && $hasPendingOrders) {
                $waitTime = 5;  // 等待5秒
                echo "还有订单待处理，{$waitTime}秒后将进行下一轮获取...\n";
                sleep($waitTime);
            }
        }

        $totalProcessed = count($allOrders);
        echo "所有轮次共获取处理了 {$totalProcessed} 个订单\n";

        // 再次检查是否还有未处理的订单
        if ($this->checkPendingOrders($year, $month)) {
            echo "警告: 仍有部分订单未能成功处理，可以稍后再次运行来尝试处理这些订单\n";
        } else {
            echo "所有订单已成功处理\n";
        }

        return $allOrders;
    }

    /**
     * 获取单个订单的详细信息
     *
     * @param object $shop 店铺信息
     * @param string $orderId 订单ID
     * @return array|null 订单详细信息数组，如果获取失败则返回null
     */
    public function getOrderDetail($shop, string $orderId): ?array
    {
        try {
            // 调用淘宝API获取订单详情，使用重试机制
            $result = $this->callTaobaoApiWithRetry(
                $shop,
                "taobao.trade.fullinfo.get",
                [
                    "tid"    => $orderId,
                    "fields" => "tid,payment_method,type,status,payment,buyer_message,seller_memo,created,pay_time,consign_time,end_time,receiver_name,receiver_address,receiver_mobile,receiver_phone,orders,promotion_details,received_payment"
                ]
            );

            echo str_repeat("=", 50) . "\n";
            print_r($result);
            echo str_repeat("=", 50) . "\n"; // todo ....

            // 解析API返回结果
            if (isset($result["trade_fullinfo_get_response"]) && isset($result["trade_fullinfo_get_response"]["trade"])) {
                return $result["trade_fullinfo_get_response"]["trade"];
            } else {
                echo "获取淘宝订单 {$orderId} 详情失败: API返回数据格式错误\n";
                return null;
            }

        } catch (Exception $e) {
            echo "获取淘宝订单 {$orderId} 详情失败: " . $e->getMessage() . "\n";
            return null;
        }
    }

    /**
     * 从订单详情中提取子订单信息
     *
     * @param array $orderDetail 订单详情数据
     * @return array 子订单列表
     */
    private function extractSubOrders(array $orderDetail): array
    {
        $subOrders = [];

        // 检查是否有子订单
        if (isset($orderDetail["orders"]) && isset($orderDetail["orders"]["order"])) {
            $orderList = $orderDetail["orders"]["order"];

            // 获取主订单信息
            $mainOrderId   = $orderDetail["tid"] ?? "";
            $status        = $orderDetail["status"] ?? "";
            $paymentMethod = $orderDetail["payment_method"] ?? "";
            $created       = $orderDetail["created"] ?? "";
            $payTime       = $orderDetail["pay_time"] ?? "";
            $consignTime   = $orderDetail["consign_time"] ?? "";
            $endTime       = $orderDetail["end_time"] ?? "";

            // 处理每个子订单
            foreach ($orderList as $subOrder) {
                // 复制主订单信息到子订单
                $subOrderData = [
                    "main_order_id"    => $mainOrderId,
                    "tid"              => $mainOrderId,
                    "status"           => $subOrder["status"] ?? $status,  // 优先使用子订单状态
                    "payment_method"   => $paymentMethod,
                    "created"          => $created,
                    "pay_time"         => $payTime,
                    "consign_time"     => $consignTime,
                    "end_time"         => $endTime,
                    // 添加子订单特有信息
                    "sub_order_id"     => $subOrder["oid"] ?? "",
                    "title"            => $subOrder["title"] ?? "",
                    "num"              => $subOrder["num"] ?? 1,
                    "price"            => floatval($subOrder["price"] ?? 0),
                    "payment"          => floatval($subOrder["payment"] ?? 0),  // 子订单金额
                    "divide_order_fee" => floatval($subOrder["divide_order_fee"] ?? 0),  // 分摊后的实付金额
                    "discount_fee"     => floatval($subOrder["discount_fee"] ?? 0),  // 优惠金额
                    "refund_status"    => $subOrder["refund_status"] ?? "NO_REFUND",
                    "total_fee"        => floatval($subOrder["total_fee"] ?? 0)  // 总金额
                ];

                $subOrders[] = $subOrderData;
            }
        }

        // 如果没有子订单，返回原始订单作为子订单
        if (empty($subOrders)) {
            $subOrders[] = $orderDetail;
        }

        return $subOrders;
    }

    /**
     * 获取指定店铺和月份的订单
     */
    private function fetchOrdersForMonth($shop, int $year, int $month): array
    {
        // 设置月份的开始和结束时间
        $startDate = new DateTime("{$year}-{$month}-01");
        if ($month == 12) {
            $endDate = new DateTime(($year + 1) . "-01-01");
            $endDate->modify('-1 second');
        } else {
            $endDate = new DateTime("{$year}-" . ($month + 1) . "-01");
            $endDate->modify('-1 second');
        }

        // $endDate = new DateTime("{$year}-{$month}-02"); // todo ..........

        // 格式化为API所需的时间格式
        $startCreated = $startDate->format("Y-m-d H:i:s");
        $endCreated   = $endDate->format("Y-m-d H:i:s");

        echo "查询时间范围: " . $startDate->format('Y-m-d') . " 至 " . $endDate->format('Y-m-d') . "\n";

        // 获取今天的日期
        $todayDate = date('Y-m-d');

        // 获取所有订单ID
        $allOrderIds = [];
        $pageNo      = 1;
        $pageSize    = 40;  // 每页订单数量
        $hasNext     = true;

        // 循环获取所有页的订单ID
        echo "正在获取店铺 " . $shop->getShopName() . " 的订单ID列表...\n";
        while ($hasNext) {
            try {
                // 使用重试机制调用API，并减少获取的字段数量
                $result = $this->callTaobaoApiWithRetry(
                    $shop,
                    "taobao.trades.sold.get",
                    [
                        "start_created" => $startCreated,
                        "end_created"   => $endCreated,
                        "status"        => "ALL",  // 获取所有状态的订单
                        "page_no"       => $pageNo,
                        "page_size"     => $pageSize,
                        // 只获取订单基本信息，详细信息通过getOrderDetail单独获取
                        "fields"        => "tid,status,created"
                    ]
                );

                // 解析API返回结果
                if (isset($result["trades_sold_get_response"])) {
                    $response = $result["trades_sold_get_response"];

                    // 获取订单列表
                    if (isset($response["trades"]) && isset($response["trades"]["trade"])) {
                        $tradeList = $response["trades"]["trade"];
                        foreach ($tradeList as $tradeData) {
                            $allOrderIds[] = strval($tradeData["tid"] ?? "");
                        }
                    }

                    // 检查是否有下一页 - 使用总数和页码计算
                    $totalResults = intval($response["total_results"] ?? 0);
                    echo "第 {$pageNo} 页获取到 " . count($allOrderIds) . " 个订单ID，总订单数: {$totalResults}\n";

                    if ($totalResults > $pageNo * $pageSize) {
                        $pageNo++;
                        $hasNext = true;
                    } else {
                        $hasNext = false;
                    }
                } else {
                    echo "淘宝API返回数据格式错误: " . print_r($result, true) . "\n";
                    break;
                }

            } catch (Exception $e) {
                echo "获取淘宝订单第 {$pageNo} 页失败: " . $e->getMessage() . "\n";
                break;
            }
        }

        echo "总共获取到 " . count($allOrderIds) . " 个订单ID\n";

        // 将所有订单ID保存到处理表中
        $this->saveOrderIdsToProcessTable($allOrderIds, $todayDate);

        // 获取待处理的订单ID
        $pendingOrderIds = $this->getPendingOrderIds($allOrderIds, $todayDate);

        // 获取订单详情
        $orders      = [];
        $totalOrders = count($pendingOrderIds);
        echo "需要处理 {$totalOrders} 个订单（今日待处理状态）\n";

        foreach ($pendingOrderIds as $index => $orderId) {
            try {
                $currentIndex = $index + 1;
                echo "正在获取订单详情 ({$currentIndex}/{$totalOrders}): {$orderId}\n";

                // 获取订单详情
                $orderDetail = $this->getOrderDetail($shop, $orderId);
                if (!$orderDetail) {
                    echo "获取订单 {$orderId} 详情失败，跳过\n";
                    continue;
                }

                // 提取子订单信息
                $subOrders = $this->extractSubOrders($orderDetail);

                // 处理每个子订单
                foreach ($subOrders as $subOrder) {
                    // 转换为Order对象
                    $order = $this->convertToOrderObject($shop, $subOrder);
                    if ($order) {
                        // 保存订单到本地数据库
                        $this->saveToLocalDb($order);
                        $orders[] = $order;
                    }
                }

                // 标记主订单为已处理
                $this->markOrderAsProcessed($orderId);

            } catch (Exception $e) {
                echo "处理订单 {$orderId} 失败: " . $e->getMessage() . "\n";
            }
        }

        echo "成功处理 " . count($orders) . " 个订单\n";
        return $orders;
    }

    /**
     * 将淘宝API返回的交易数据转换为Order对象
     */
    private function convertToOrderObject($shop, array $tradeData): ?object
    {
        try {
            // 获取订单ID（支持子订单）
            $orderId     = strval($tradeData["sub_order_id"] ?? $tradeData["tid"] ?? "");
            $mainOrderId = strval($tradeData["main_order_id"] ?? $tradeData["tid"] ?? "");

            if (empty($orderId)) {
                echo "订单ID为空，跳过\n";
                return null;
            }

            // 获取订单基本信息
            $statusCode = $tradeData["status"] ?? "";

            // 获取子订单退款状态
            $refundStatus = $tradeData["refund_status"] ?? "NO_REFUND";

            // 处理金额，优先使用子订单的divide_order_fee作为商家应收金额
            $divideOrderFee = floatval($tradeData["divide_order_fee"] ?? 0);
            $payment        = floatval($tradeData["payment"] ?? 0);
            // 如果是子订单且有divide_order_fee，则使用divide_order_fee作为商家实收
            $receivedPayment = (isset($tradeData["sub_order_id"]) && $divideOrderFee > 0)
                ? $divideOrderFee
                : floatval($tradeData["received_payment"] ?? 0);

            $paymentMethodCode = $tradeData["payment_method"] ?? "";
            $paymentMethod     = "其他";
            if ($paymentMethodCode == "wx") {
                $paymentMethod = "微信支付";
            } elseif ($paymentMethodCode == "alipay") {
                $paymentMethod = "支付宝支付";
            }

            // 根据订单状态和退款状态确定最终状态
            $paymentStatus = "UNKNOWN";

            // 优先判断退款状态
            if ($refundStatus == "SUCCESS") {
                // 退款成功，订单状态为CLOSED
                $paymentStatus = "CLOSED";
            } elseif ($statusCode == "TRADE_CLOSED") {
                // 付款以后用户退款成功，交易自动关闭
                $paymentStatus = "CLOSED";
            } elseif ($statusCode == "TRADE_CLOSED_BY_TAOBAO") {
                // 付款以前，卖家或买家主动关闭交易
                $paymentStatus = "CANCELLED";
            } else {
                // 使用基本状态映射
                $paymentStatus = self::ORDER_STATUS_MAP[$statusCode] ?? "UNKNOWN";
            }

            // 解析时间
            $paymentTime = null;
            if (!empty($tradeData["pay_time"])) {
                $paymentTime = DateTime::createFromFormat("Y-m-d H:i:s", $tradeData["pay_time"]);
            }

            $shippingTime = null;
            if (!empty($tradeData["consign_time"])) {
                $shippingTime = DateTime::createFromFormat("Y-m-d H:i:s", $tradeData["consign_time"]);
            }

            // 获取商品名称
            $title = $tradeData["title"] ?? "";

            // 创建Order对象
            $order = new Order(
                $orderId,
                $paymentStatus,
                $paymentTime,
                $shippingTime,
                $shop->getShopName(),
                $receivedPayment,
                $payment,
                $paymentMethod
            );

            // 添加原始数据，方便后续处理
            $order->orderData = $tradeData;

            // 添加主订单ID，用于关联子订单
            $order->mainOrderId = $mainOrderId;

            // 添加商品标题
            $order->title = $title;

            // 添加退款状态
            $order->refundStatus = $refundStatus;

            return $order;
        } catch (Exception $e) {
            echo "转换淘宝订单数据失败: " . $e->getMessage() . "\n";
            return null;
        }
    }

    /**
     * 将订单保存到本地数据库
     */
    private function saveToLocalDb($order)
    {
        try {
            // 获取订单数据
            $orderData = $order->orderData ?? [];

            // 获取主订单ID
            $mainOrderId = $order->mainOrderId ?? $order->orderId;

            // 获取退款状态
            $refundStatus = $order->refundStatus ?? "NO_REFUND";

            // 映射订单状态
            $statusCode = $order->paymentStatus;

            // 判断状态值
            if ($statusCode == "PENDING") {  // 待付款
                echo "订单 {$order->orderId} 状态为待付款，跳过保存\n";
                return;
            }

            // 映射订单状态到枚举值
            $status = null;

            // 根据支付状态和退款状态确定最终状态
            if ($statusCode == "CANCELLED") {
                $status = OrderStatus::CANCELLED;
            } elseif ($statusCode == "CLOSED" || $refundStatus == "SUCCESS") {
                $status = OrderStatus::REFUNDED;
            } elseif ($statusCode == "COMPLETED") {
                $status = OrderStatus::COMPLETED;
            } elseif ($statusCode == "SHIPPED") {
                $status = OrderStatus::SHIPPED;
            } elseif (in_array($statusCode, ["PROCESSING"])) {
                $status = OrderStatus::PAID;
            }

            if ($status === null) {
                echo "订单 {$order->orderId} 状态 {$statusCode} 无法映射，跳过保存\n";
                return;
            }

            // 处理订单金额相关数据
            $payment         = $order->receivedPayment;  // 订单金额
            $merchantReceive = $order->payment;  // 商家实收金额
            $paymentMethod   = $order->paymentMethod;  // 支付渠道

            // 根据订单状态设置退款金额
            if (in_array($status, [OrderStatus::REFUNDED, OrderStatus::CANCELLED])) {
                // 如果订单状态是已退款或已取消，退款金额等于商家实收
                $refundAmount       = $merchantReceive;
                $merchantReceivable = 0;  // 应收金额
            } else {
                // 其他状态按正常计算退款金额
                $refundAmount       = $payment - $merchantReceive;
                $merchantReceivable = $merchantReceive;  // 应收金额
            }

            // 判断是否已发货
            $isShipped       = $order->shippingTime !== null;
            $shippingTimeSec = $order->shippingTime ? $order->shippingTime->getTimestamp() : null;

            // 获取结算单数据
            $settlementsQuery = "
            SELECT
                SUM(settlement_amount) as total_settlement,
                MAX(settlement_time) as latest_settlement
            FROM settlements
            WHERE order_id = ?
            ";
            $result           = $this->ordersManager->executeQueryOne($settlementsQuery, [$order->orderId]);

            // 从结算单获取回款信息
            $receivedAmount = 0;
            $paymentDate    = null;
            if ($result) {
                $totalSettlement  = $result[0];
                $latestSettlement = $result[1];
                $receivedAmount   = $totalSettlement ?: 0;
                $paymentDate      = $latestSettlement;
            } else {
                $receivedAmount = 0;
                $paymentDate    = null;
            }

            // 初始化ERP相关金额
            $u8c029Amount    = 0;
            $u8c515Amount    = 0;
            $tPlus002Amount  = 0;
            $tPlus008Amount  = 0;
            $billDate        = "";
            $salesOrderTotal = 0;

            // 查询ERP订单信息
            $erpOrder          = new ERPOrder();
            $erpOrder->orderId = $order->orderId;
            $erpResult         = $this->orderQuery->querySingleOrder($erpOrder);

            print_r($erpResult);

            // 根据ERP查询结果更新金额
            if ($erpResult->errorCode == 0) {  // 查询成功
                $amount   = floatval($erpResult->amount);
                $billDate = $erpResult->billDate;
                // 根据账套ID分配金额
                if ($erpResult->erpSystem == "U8C") {
                    if ($erpResult->accountId == "u8c_029") {
                        $u8c029Amount = $amount;
                    } elseif ($erpResult->accountId == "u8c_515") {
                        $u8c515Amount = $amount;
                    }
                } elseif ($erpResult->erpSystem == "T+") {
                    if ($erpResult->accountId == "t_plus_002") {
                        $tPlus002Amount = $amount;
                    } elseif ($erpResult->accountId == "t_plus_008") {
                        $tPlus008Amount = $amount;
                    }
                }
            }

            // 计算销货单合计
            $salesOrderTotal = ($u8c029Amount + $u8c515Amount + $tPlus002Amount + $tPlus008Amount);
            $unshippedAmount = $merchantReceivable - $salesOrderTotal;  // 未发货金额 = 应收金额 - 销货单金额合计

            // 根据店铺名称和支付方式确定支付渠道
            $shopName       = $order->shopName;
            $paymentChannel = "";

            // 判断是支付宝还是微信支付
            $isAlipay = ($paymentMethod == "支付宝支付" || $paymentMethod == "alipay");
            $isWechat = ($paymentMethod == "微信支付" || $paymentMethod == "wx");

            // 根据店铺名称和支付方式设置支付渠道
            if (strpos($shopName, "佰酿美酒天猫") !== false) {
                if ($isAlipay) {
                    $paymentChannel = "科技支付宝-bainiangmeiiutianmao";
                } elseif ($isWechat) {
                    $paymentChannel = "佰酿美酒天猫平台";
                }
            } elseif (strpos($shopName, "佰酿科技（天猫旗舰店）") !== false) {
                if ($isAlipay) {
                    $paymentChannel = "科技支付宝<EMAIL>";
                } elseif ($isWechat) {
                    $paymentChannel = "天猫旗舰店平台";
                }
            } elseif (strpos($shopName, "桃公子淘宝店") !== false) {
                if ($isAlipay) {
                    $paymentChannel = "科技支付宝-taogongzitaojiuguan";
                } elseif ($isWechat) {
                    $paymentChannel = "桃公子淘宝店平台";
                }
            } elseif (strpos($shopName, "天猫（法国南部葡萄酒官方旗舰店）") !== false) {
                if ($isAlipay) {
                    $paymentChannel = "法南天猫支付宝";
                } elseif ($isWechat) {
                    $paymentChannel = "法南天猫平台余额";
                }
            } elseif (strpos($shopName, "酒遇喵掌柜") !== false) {
                if ($isAlipay) {
                    $paymentChannel = "科技支付宝-jiuyumiaozhanggui";
                } elseif ($isWechat) {
                    $paymentChannel = "酒遇喵掌柜平台余额";
                }
            } elseif (strpos($shopName, "美尼多") !== false) {
                if ($isAlipay) {
                    $paymentChannel = "科技支付卡-shyanjiusuo";
                } elseif ($isWechat) {
                    $paymentChannel = "美尼多天猫旗舰店平台";
                }
            } else {
                // 默认支付渠道
                $paymentChannel = $paymentMethod;
            }

            // 构建订单数据
            $data = [
                TableFields::ORDER_ID            => $order->orderId,
                TableFields::MAIN_ORDER_ID       => $mainOrderId,
                TableFields::PLATFORM_TYPE       => $this->platformType->value,
                TableFields::PLATFORM_NAME       => $order->shopName,
                TableFields::ORDER_STATUS        => $status,
                TableFields::SHIPPING_TIME       => $shippingTimeSec,
                TableFields::MERCHANT_RECEIVABLE => $merchantReceive,  // 商家实收金额
                TableFields::REFUND_AMOUNT       => $refundAmount,  // 退款金额
                TableFields::ORDER_AMOUNT        => $payment,  // 订单金额
                TableFields::RECEIVABLE_AMOUNT   => $merchantReceivable,  // 应收金额
                TableFields::PAYMENT_CHANNEL     => $paymentChannel,
                // erp start
                TableFields::U8C_029_AMOUNT      => $u8c029Amount,
                TableFields::U8C_515_AMOUNT      => $u8c515Amount,
                TableFields::T_PLUS_002_AMOUNT   => $tPlus002Amount,
                TableFields::T_PLUS_008_AMOUNT   => $tPlus008Amount,
                TableFields::SALES_ORDER_TOTAL   => $salesOrderTotal,
                TableFields::SALES_ORDER_DATE    => $billDate,
                // erp end
                TableFields::UNSHIPPED_AMOUNT    => $unshippedAmount,
                TableFields::RECEIVED_AMOUNT     => $receivedAmount,  // 回款金额
                TableFields::PAYMENT_DATE        => $paymentDate,
                TableFields::PENDING_PAYMENT     => max(0, $merchantReceivable - $receivedAmount)
            ];

            // 打印调试信息
            echo "订单 {$order->orderId} (主订单: {$mainOrderId}) 状态映射: {$statusCode} -> {$status->value} 退款状态: {$refundStatus} 支付方式: {$paymentMethod}, 支付渠道: {$paymentChannel}\n";

            try {
                // 检查订单是否存在
                $exists = $this->ordersManager->orderExists(strval($order->orderId));

                if ($exists) {
                    // 订单存在，执行更新
                    $this->ordersManager->updateData($order->orderId, $data);
                    echo "更新订单: {$order->orderId} (主订单: {$mainOrderId})\n";
                } else {
                    // 订单不存在，执行插入
                    $this->ordersManager->insertData($data);
                    echo "插入订单: {$order->orderId} (主订单: {$mainOrderId})\n";
                }

            } catch (Exception $e) {
                echo "保存订单到本地数据库失败: " . $e->getMessage() . "\n";
            }

        } catch (Exception $e) {
            echo "订单状态处理错误 ({$order->orderId}): " . $e->getMessage() . "\n";
        }
    }

    /**
     * 带重试机制的淘宝API调用
     */
    private function callTaobaoApiWithRetry($shop, string $method, array $params): array
    {
        $retries       = 0;
        $lastException = null;

        while ($retries < $this->maxRetries) {
            try {
                // 调用原有的API方法
                $result = $this->callTaobaoApi($shop, $method, $params);
                return $result;
            } catch (APIConnectionError|PlatformRunError $e) {
                $lastException = $e;
                $retries++;
                echo "API调用失败，正在重试 ({$retries}/{$this->maxRetries}): " . $e->getMessage() . "\n";
                // 等待一段时间后重试
                sleep($this->retryDelay * $retries);  // 递增等待时间
            }
        }

        // 所有重试都失败后，抛出最后一个异常
        if ($lastException) {
            throw $lastException;
        } else {
            throw new PlatformRunError("API调用失败，已重试 {$this->maxRetries} 次");
        }
    }

    /**
     * 调用淘宝开放平台API
     */
    private function callTaobaoApi($shop, string $method, array $params): array
    {
        try {
            // 检查shop对象是否有必要的API凭证属性
            if (!$shop->hasAppKey() || !$shop->hasAppSecret() || !$shop->hasAccessToken()) {
                throw new \InvalidArgumentException("店铺 " . $shop->getShopName() . " 缺少API凭证属性");
            }

            $appKey    = $shop->getAppKey();
            $appSecret = $shop->getAppSecret();
            $session   = $shop->getAccessToken();

            // 准备请求参数
            $timestamp = date("Y-m-d H:i:s");

            $requestParams = [
                "method"      => $method,
                "app_key"     => $appKey,
                "timestamp"   => $timestamp,
                "format"      => "json",
                "v"           => self::API_VERSION,
                "sign_method" => "md5",
                "session"     => $session
            ];

            // 合并业务参数
            $requestParams = array_merge($requestParams, $params);

            // 计算签名
            $requestParams["sign"] = $this->calculateSign($requestParams, $appSecret);

            // 发送请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, self::API_URL);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($requestParams));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response  = curl_exec($ch);
            $httpCode  = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            // 检查cURL错误
            if ($curlError) {
                throw new APIConnectionError("淘宝API连接错误: " . $curlError);
            }

            // 检查响应状态
            if ($httpCode !== 200) {
                throw new APIConnectionError("淘宝API请求失败，HTTP状态码: " . $httpCode);
            }

            // 解析响应数据
            $result = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new PlatformRunError("淘宝API返回数据解析失败: " . json_last_error_msg());
            }

            // 检查是否有错误
            if (isset($result["error_response"])) {
                $error     = $result["error_response"];
                $errorCode = $error["code"] ?? "unknown";
                $errorMsg  = $error["msg"] ?? "Unknown error";
                $subCode   = $error["sub_code"] ?? "";
                $subMsg    = $error["sub_msg"] ?? "";

                $errorInfo = "错误码: {$errorCode}, 信息: {$errorMsg}";
                if ($subCode) {
                    $errorInfo .= ", 子错误码: {$subCode}, 子信息: {$subMsg}";
                }

                throw new PlatformRunError("淘宝API返回错误: " . $errorInfo);
            }

            return $result;

        } catch (Exception $e) {
            if ($e instanceof APIConnectionError || $e instanceof PlatformRunError) {
                throw $e;
            }
            throw new PlatformRunError("调用淘宝API失败: " . $e->getMessage());
        }
    }

    /**
     * 计算淘宝API请求签名
     */
    private function calculateSign(array $params, string $appSecret): string
    {
        // 按字母顺序排序参数
        ksort($params);

        // 拼接参数
        $paramStr = "";
        foreach ($params as $key => $value) {
            if ($value !== null && $value !== "") {
                $paramStr .= $key . $value;
            }
        }

        // 加上app_secret前后缀
        $signStr = $appSecret . $paramStr . $appSecret;

        // 计算MD5
        return strtoupper(md5($signStr));
    }
}