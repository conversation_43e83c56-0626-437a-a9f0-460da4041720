<?php
// 应用公共文件

use app\service\es\Es;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use think\facade\Db;
use think\facade\Log;
use think\Response;

/**
 * 接口获取返回方法
 * <AUTHOR> <<EMAIL>>
 * @Date 2021/09/13 14:04
 * @param array $options 返回参数
 * @param int $code 状态码
 * @param string $error_msg 错误信息
 * @param array $header 返回头信息
 * @return Response
 */
if (!function_exists('throwResponse')) {
    function throwResponse($options = [], int $code = 0, string $error_msg = '', array $header = []): Response
    {
        $data['error_code'] = $code;
        $data['error_msg']  = $error_msg;
        $data['data']       = $options;
        return Response::create($data, 'json', 200)->header($header);
    }
}

/**
 * 自定义异常
 * <AUTHOR> <<EMAIL>>
 * @Date 2021/09/13 14:04
 * @param string $msg 错误信息
 * @param string $level 日志级别
 * @param int $code 状态码
 * @return Exception
 */
if (!function_exists('excep')) {
    function excep($msg = '', $level = 'info', $code = 10002)
    {
        throw new app\CustomException($msg, $level, $code);
    }
}

/**
 * curl请求
 * <AUTHOR> <<EMAIL>>
 * @Date 2021/09/13 14:04
 * @param string $url 请求地址
 * @param string $http 请求方式
 * @param array $data 请求数据
 * @param bool $cert 是否需要证书
 * @param int $timeout 超时时间
 * @return object
 */
if (!function_exists('httpCurl')) {
    function httpCurl($url, $http = 'get', $data = [], $timeout = 60, $headers = [])
    {
        $headers[] = 'Content-Type: application/json;charset=utf-8';

        $curl = curl_init(); //初始化
        curl_setopt($curl, CURLOPT_URL, $url); //设置抓取的url
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书检查
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false); // 从证书中检查SSL加密算法是否存在
        curl_setopt($curl, CURLOPT_HEADER, false); //设置头文件的信息作为数据流输出
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers); //设置头信息
        if ($http == 'post') {
            curl_setopt($curl, CURLOPT_POST, true); //设置post方式提交
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data); //设置post数据
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); //设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_TIMEOUT, $timeout); //设置超时时间
        $response = curl_exec($curl); //执行命令
        curl_close($curl); //关闭URL请求
        // print_r($response); //显示获得的数据
        return $response;
    }
}

if (!function_exists('serviceReturn')) {
    /**
     * service 返回数据
     * @param bool $status
     * @param string $data
     * @param string $msg
     * @return array
     * <AUTHOR>
     */
    function serviceReturn(bool $status = true, $data = '', string $msg = ''): array
    {
        return ['status' => $status, 'data' => $data, 'msg' => $msg];
    }
}

if (!function_exists('post_url')) {
    /**
     * 模拟POST提交
     * @param string $url 地址
     * @param array | string $data 提交的数据
     * @return string 返回结果
     * <AUTHOR>
     */
    function post_url($url, $data, $header = [])
    {
        if (is_array($data)) {
            $data = http_build_query($data);
        }
        // 启动一个CURL会话
        $curl = curl_init();
        // 要访问的地址
        curl_setopt($curl, CURLOPT_URL, $url);
        // 对认证证书来源的检查
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        // 从证书中检查SSL加密算法是否存在
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        // 模拟用户使用的浏览器
        curl_setopt($curl, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        // 发送一个常规的Post请求
        curl_setopt($curl, CURLOPT_POST, 1);
        // Post提交的数据包
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        // 设置超时限制 防止死循环
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        // 显示返回的Header区域内容
        curl_setopt($curl, CURLOPT_HEADER, 0);
        // 获取的信息以文件流的形式返回
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        $tmpInfo = curl_exec($curl); // 执行操作
        //        if(curl_errno($curl))
        //        {
        //            echo 'Errno'.curl_error($curl);//捕抓异常
        //        }
        curl_close($curl); // 关闭CURL会话
        return $tmpInfo; // 返回数据
    }
}

if (!function_exists('get_url')) {
    /**
     * 通过URL获取页面信息
     * @param $url 地址
     * @return mixed 返回页面信息
     */
    function get_url($url, $header = [])
    {
        $ch = curl_init();
        //设置访问的url地址
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        //不输出内容
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($ch);
        curl_close($ch);
        return $result;
    }
}

if (!function_exists('get_interior_http_response')) {
    /**
     * 解析内部访问数据
     * @param $json
     * @return mixed
     * <AUTHOR>
     */
    function get_interior_http_response($json)
    {
        $result = json_decode($json, true);
        if ($result['error_code'] == '0') {
            return $result['data'];
        } else {
            return null;
        }
    }
}

if (!function_exists("pushMqlist")) {

    function pushMqlist(string $exchange_name, string $routing_key, array $mqData)
    {
        $client   = new \GuzzleHttp\Client(['timeout' => 3]);
        $response = $client->post(env('ITEM.QUEUE_URL'), [
            'json'    => [
                'exchange_name' => $exchange_name,
                'routing_key'   => $routing_key,
                'data'          => base64_encode(json_encode($mqData))
            ],
            'headers' => [
                'Content-Type'   => 'application/json',
                "vinehoo-client" => "tp6-wine-wiki", //默认磐石系统
            ]
        ]);
        if ($response->getStatusCode() != 200) {
            \think\Log::ERROR("队列同步失败" . json_encode($mqData));
            excep("数据修改成功，队列同步失败");
        }
        return $response;
    }
}

/**
 * 解析 es 返回数据
 * @param array $es_data
 * @return array
 * <AUTHOR>
 */
function get_es_result(array $es_data): array
{
    $data['total'] = 0;
    $data['list']  = [];
    if ($es_data['hits']['total']['value'] < 1) {
        return $data;
    }
    $data['total'] = $es_data['hits']['total']['value'];
    foreach ($es_data['hits']['hits'] as $val) {
        $data['list'][] = $val['_source'];
    }
    return $data;
}

/**
 * 获取区域数据
 * @return void
 */
function getRegionalData($ids)
{
    $domain = env('ITEM.USER_URL');
    $method = '/user/v3/regional/getBatchInfo?id=' . $ids;

    $url = $domain . $method;

    $data = get_url($url);

    return $data;
}

function getproid(array $data, string $key, string $field)
{

    if ($data == []) return [];
    foreach ($data as $k => $v) {
        if ($v[$key] == []) {
            $data[$k]['regionalname'] = "";
        } else {
            $code = array_column($v[$key], $field);

            $param = implode(',', $code);

            $RegionalData             = getRegionalData($param);
            $RegionalData             = json_decode($RegionalData, true);
            $data[$k]['regionalname'] = getproname($RegionalData['data']['list']);
        }

    }

    return $data;
}

function getproname(array $param)
{

    $code  = array_values($param);
    $names = array_column($code, 'name');
    return implode(',', $names);
}

function getareaData($resouce)
{
    //省市区
    $province = array_column($resouce, 'pro_id');
    $city     = array_column($resouce, 'cid');
    $area     = array_column($resouce, 'aid');

    $data = array_merge($province, $city, $area);

    $param        = implode(",", $data);
    $RegionalData = getRegionalData($param);
    $RegionalData = json_decode($RegionalData, true);

    if (!$RegionalData) return [];
    if ($RegionalData['error_code'] == 0) return $RegionalData['data']['list'];

    return [];
}


function getareabylonlats($method, $data)
{
    $client = new \GuzzleHttp\Client(['timeout' => 3]);

    echo env('ITEM.DISTANCE_URL') . $method;
    echo(json_encode($data));
    $response = $client->post(env('ITEM.DISTANCE_URL') . $method, [
        'json'    => [
            //            'data' => base64_encode(json_encode($data))
            'data' => json_encode($data)
        ],
        'data'    => base64_encode(json_encode($data)),
        'headers' => [
            'Content-Type'   => 'application/json',
            "vinehoo-client" => "tp6-vmall", //默认磐石系统
        ]
    ]);
    if ($response->getStatusCode() != 200) {
        \think\Log::ERROR("获取区域失败" . json_encode($data));
        excep("根据定位获取区域失败");
    }
    //    var_dump($response);
    return $response->getBody()->getContents();
}

/**
 * @方法描述:url请求
 * <AUTHOR>
 * @Date   2022/08/03
 * @param string $url 请求url
 * @param array $data 请求参数
 * @param array $haeder 请求头
 * @param string $method 请求的方式
 * @param int $timeout 超时时间，单位s
 * @param bool $sync 不启用异步，默认为true
 * @return Response
 */
function curlRequest($url, $data = [], $haeder = [], $method = 'POST', $timeout = 3, $sync = True)
{
    if ($method == 'POST' && !is_array($data)) {
        $haeder[] = 'Content-Type:application/json';
    }
    $ch = curl_init();
    if (is_array($data)) {
        $data = http_build_query($data);
    }
    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, True);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    } else {
        if (!empty($data)) curl_setopt($ch, CURLOPT_URL, "{$url}?{$data}");
        else curl_setopt($ch, CURLOPT_URL, $url);
    }
    if (strlen($url) > 5 && strtolower(substr($url, 0, 5)) == "https") {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    }
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    if ($haeder) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $haeder);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, $sync);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_NOBODY, !$sync);
    $return = curl_exec($ch);
    curl_close($ch);
    #记录日志
    $data = is_array($data) ? json_encode($data) : $data;
    Log::info('请求url：' . $url . '，请求参数：' . $data . '，请求结果：' . $return);
    return $return;
}

/**
 * @方法描述:数据加解密
 * <AUTHOR>
 * @Date   2022/08/03
 * @param array $param 参数
 * @param string $type 类型：D解密，E加密
 * @param int $i 请求次数
 * @return Response
 */
function cryptionDeal($param, $type = 'D', $i = 1)
{
    $url = $type == 'D' ? '/v1/decrypt' : '/v1/encrypt';

    $data   = [
        'orig_data' => array_values($param['orig_data']),
        'from'      => 'tp6-authority',
        'uid'       => strval($param['uid']),
        'operator'  => strval($param['operator'])
    ];
    $haeder = ['Content-Type:application/json'];
    $res    = curlRequest(env('ITEM.CRYPTION_ADDRESS') . $url, json_encode($data), $haeder);
    $res    = $res ?? '';
    $res    = json_decode($res, true);
    #失败重试
    if (empty($res['data']) && $i < 3) {
        $i++;
        $res = cryptionDeal($param, $type, $i);
    }
    return $res['data'] ?? [];
}

function getdistance($postdata)
{
    $method = '/vmall_distance/v3/deliveries/distance';
    $url    = env('ITEM.DISTANCE_URL') . $method;
    $code   = httpCurl($url, 'post', json_encode($postdata), 3);
    $result = json_decode($code, true);

//    var_dump($result);
    if (!$result || $result['error_code'] != 0) excep("获取区域失败");
    return $result['lonlat'];
}

/**
 * @方法描述:中文字符串截取
 * <AUTHOR>
 * @Date   2022/08/03
 * @param string $string 原始字符串
 * @param string $replacement 替换字符
 * @param int $start 开始位置
 * @param int $length 替换长度
 * @param string $encoding 编码
 * @return string 截取字符串
 */
if (function_exists('mb_substr_replace') === false) {
    function mb_substr_replace($string, $replacement, $start, $length = null, $encoding = null)
    {
        if (extension_loaded('mbstring') === true) {
            $string_length = (is_null($encoding) === true) ? mb_strlen($string) : mb_strlen($string, $encoding);

            if ($start < 0) {
                $start = max(0, $string_length + $start);
            } else if ($start > $string_length) {
                $start = $string_length;
            }

            if ($length < 0) {
                $length = max(0, $string_length - $start + $length);
            } else if ((is_null($length) === true) || ($length > $string_length)) {
                $length = $string_length;
            }

            if (($start + $length) > $string_length) {
                $length = $string_length - $start;
            }

            if (is_null($encoding) === true) {
                return mb_substr($string, 0, $start) . $replacement . mb_substr($string, $start + $length, $string_length - $start - $length);
            }

            return mb_substr($string, 0, $start, $encoding) . $replacement . mb_substr($string, $start + $length, $string_length - $start - $length, $encoding);
        }

        return (is_null($length) === true) ? substr_replace($string, $replacement, $start) : substr_replace($string, $replacement, $start, $length);
    }
}

/**
 * @方法描述:分页处理
 * <AUTHOR>
 * @param array $param 参数
 * @return array [分页起始值,返回条数]
 */
function pageHandle($param)
{
    $page  = $param['page'] ?? 1;
    $limit = $param['limit'] ?? 10;
    $page  = $page > 0 ? $page : 1;
    $limit = $limit > 0 ? $limit : 10;
    #分页起始值
    $offsetMain = ($page - 1) * $limit;

    return [intval($offsetMain), intval($limit)];
}

/**
 * @方法描述:将一个平面的二维数组按照指定的字段转换为树状结构
 * <AUTHOR>
 * @Date   2021/09/07
 * @param array $arr 数据源
 * @param string $key_node_id 节点ID字段名
 * @param string $key_parent_id 节点父ID字段名
 * @param string $key_childrens 保存子节点的字段名
 * @param boolean $refs 是否在返回结果中包含节点引用
 * @return array 树形结构的数组
 */
function toTree($arr, $key_node_id, $key_parent_id = 'pid', $key_childrens = 'children', $treeIndex = false, &$refs = null)
{
    $refs = array();
    foreach ($arr as $offset => $row) {
        $arr[$offset][$key_childrens] = array();
        $refs[$row[$key_node_id]]     = &$arr[$offset];
    }

    $tree = array();
    foreach ($arr as $offset => $row) {
        $parent_id = $row[$key_parent_id];
        if ($parent_id) {
            if (!isset($refs[$parent_id])) {
                if ($treeIndex) {
                    $tree[$offset] = &$arr[$offset];
                } else {
                    $tree[] = &$arr[$offset];
                }
                continue;
            }
            $parent = &$refs[$parent_id];
            if ($treeIndex) {
                $parent[$key_childrens][$offset] = &$arr[$offset];
            } else {
                $parent[$key_childrens][] = &$arr[$offset];
            }
        } else {
            if ($treeIndex) {
                $tree[$offset] = &$arr[$offset];
            } else {
                $tree[] = &$arr[$offset];
            }
        }
    }

    return $tree;
}

/**
 * 删除文件夹
 */
if (!function_exists('deldir')) {
    function deldir($dir)
    {
        if (!is_dir($dir)) return true;

        //先删除目录下的文件：
        $dh = opendir($dir);
        while ($file = readdir($dh)) {
            if ($file != "." && $file != "..") {
                $fullpath = $dir . "/" . $file;
                if (!is_dir($fullpath)) {
                    unlink($fullpath);
                } else {
                    deldir($fullpath);
                }
            }
        }

        closedir($dh);
        //删除当前文件夹：
        if (rmdir($dir)) {
            return true;
        } else {
            return false;
        }
    }
}

/**
 * 获取客户端真实IP
 */
if (!function_exists('get_client_ip')) {
    function get_client_ip()
    {
        $ip = false;
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        }
        if (!empty($_SERVER['HTTP_X_ORIGINAL_FORWARDED_FOR']) || !empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            if (!empty($_SERVER['HTTP_X_ORIGINAL_FORWARDED_FOR'])) {
                $ips = explode(', ', $_SERVER['HTTP_X_ORIGINAL_FORWARDED_FOR']);
            } else {
                $ips = explode(', ', $_SERVER['HTTP_X_FORWARDED_FOR']);
            }
            if ($ip) {
                array_unshift($ips, $ip);
                $ip = FALSE;
            }
            for ($i = 0; $i < count($ips); $i++) {
                if (!preg_match('/^(10│172.16│192.168)./i', $ips[$i])) {
                    $ip = $ips[$i];
                    break;
                }
            }
        }
        return ($ip ? $ip : $_SERVER['REMOTE_ADDR']);
    }
}

/**
 * 多图处理
 */
if (!function_exists('multiple_image_full_path')) {
    function multiple_image_full_path(string $urls): array
    {
        $product_imgs = explode(',', $urls);
        foreach ($product_imgs as &$img) {
            $img = empty($img) ? $img : env("ALIURL") . $img;
        }
        return $product_imgs;
    }
}
if (!function_exists('image_full_path')) {
    /**
     * @方法描述: 单图返回全路径
     * <AUTHOR>
     * @Date 2022/12/8 12:41
     * @param $value
     * @return string
     */
    function image_full_path($value)
    {
        if (!$value) return '';
        $host = env('ALIURL');
        if (empty($host)) return $value;
        return (false === strpos($value, $host)) ? $host . $value : $value;
    }
}

if (!function_exists('uuid')) {
    function uuid()
    {
        $chars = md5(uniqid(mt_rand(), true));
        $uuid  = substr($chars, 0, 8) . '-'
            . substr($chars, 8, 4) . '-'
            . substr($chars, 12, 4) . '-'
            . substr($chars, 16, 4) . '-'
            . substr($chars, 20, 12);
        return $uuid;
    }
}

/**
 * 时间转换计算今明几点开始
 */
if (!function_exists('sctime')) {
    function sctime($start_time)
    {
        $t = time();
        // 今日凌晨时间
        $td = strtotime(date('Y-m-d 23:59:59', $t));
        // 今日开始时间
        $tsd = strtotime(date('Y-m-d 00:00:00', $t));
        // 明天开始时间
        $tmor = strtotime("+1 day", $tsd);
        // 明天凌晨时间
        $tmtd = strtotime(date('Y-m-d 23:59:59', $tmor));
        // 计算时间
        if ($start_time >= $tsd && $start_time <= $td) {
            return '今天 ' . date('H:i:s', $start_time);
        } elseif ($start_time >= $tmor && $start_time <= $tmtd) {
            return '明天 ' . date('H:i:s', $start_time);
        }
        return date('m月d日 H:i:s', $start_time);
    }
}

/**
 * 时间转换计算今明周几点开始
 */
if (!function_exists('ectime')) {
    function ectime($end_time)
    {
        $t = time();
        // 今日凌晨时间
        $td = strtotime(date('Y-m-d 23:59:59', $t));
        // 今日开始时间
        $tsd = strtotime(date('Y-m-d 00:00:00', $t));
        // 明天开始时间
        $tmor = strtotime("+1 day", $tsd);
        // 明天凌晨时间
        $tmtd = strtotime(date('Y-m-d 23:59:59', $tmor));
        // 后天开始时间
        $htd = strtotime("+1 day", $tmor);
        // 后天凌晨时间
        $hetd = strtotime(date('Y-m-d 23:59:59', $htd));
        // 计算时间
        $week_arr = ['日', '一', '二', '三', '四', '五', '六'];
        if ($end_time >= $tsd && $end_time <= $td) {
            return '今天 ' . date('H:i:s', $end_time);
        } elseif ($end_time >= $tmor && $end_time <= $tmtd) {
            return '明天 ' . date('H:i:s', $end_time);
        } elseif ($end_time >= $htd && $end_time <= $hetd) {
            return '周' . $week_arr[date('w', $end_time)] . ' ' . date('H:i:s', $end_time);
        }
        return date('m月d日 H:i:s', $end_time);
    }
}

if (!function_exists('getMillisecond')) {
    /**
     * @方法描述:获取毫秒级时间戳
     * <AUTHOR>
     * @Date 2023/2/20 9:12
     * @return float
     */
    function getMillisecond()
    {
        list($t1, $t2) = explode(' ', microtime());
        return (float)sprintf('%.0f', (floatval($t1) + floatval($t2)) * 1000);
    }
}

function download_image($url, $format = 'xlsx')
{
    $file           = file_get_contents($url);
    $time           = time();
    $localPath      = root_path() . 'public/storage/file';
    $pic_local_path = $localPath . '/' . date("Ymd", time()) . '/' . $time . '.' . $format;
    $pic_local_url  = $localPath . '/' . date("Ymd", time());
    if (!file_exists($localPath)) {
        mkdir($localPath, 0777);
    }
    if (!file_exists($pic_local_url)) {
        mkdir($pic_local_url, 0777);
    }
    file_put_contents($pic_local_path, $file);
    return $pic_local_path;
}

function getExcelData($path, $startI)
{
    $reader = new Xls();
    try {
        $spreadsheet = $reader->load($path);
    } catch (\Exception $e) {
        $return = [
            'error_code' => 10002,
            'error_msg'  => $e->getMessage(),
            'data'       => []
        ];
        return $return;
    }
    $sheet     = $spreadsheet->getActiveSheet();
    $excelData = array();
    foreach ($sheet->getRowIterator($startI) as $row) {
        $tmp = array();
        foreach ($row->getCellIterator() as $cell) {
            $tmp[] = $cell->getFormattedValue();
        }
        $excelData[$row->getRowIndex()] = $tmp;
    }
    return [
        'error_code' => 0,
        'error_msg'  => '',
        'data'       => $excelData
    ];
}


/**
 * @方法描述: 根据uid 解密手机号 正式服
 * <AUTHOR>
 * @Date 2023/11/21 15:51
 * @param $uids
 * @param $arr_len
 * @return array[]
 */
function getPhone($uids, $arr_len = 500)
{
    $uids         = array_values(array_unique($uids));
    $enc_err_data = $user_phones = [];

    $enc_data = Db::connect('pord_mysql_user')->name('user')
        ->where('uid', 'in', $uids)
        ->where('is_delete', 0)//未删除的用户
        ->column('telephone,uid');
    $enc_data = array_chunk($enc_data, $arr_len);

    foreach ($enc_data as $enc_datum) {
        $dec_data = \Curl::cryptionDeal(array_column($enc_datum, 'telephone'));
        foreach ($enc_datum as $enc_info) {
            $telphone = $dec_data[$enc_info['telephone']] ?? '';
            //验证手机号格式 // todo...

            $enc_info['phone'] = $telphone;
            if ($telphone) {
                //解密成功手机号
                $user_phones[$enc_info['uid']] = $enc_info;
            } else {
                //解密失败
                $enc_err_data[$enc_info['uid']] = $enc_info;
            }
        }
    }

    if (!empty($enc_err_data)) {
        print_r('获取手机号失败用户: ' . implode(',', array_keys($enc_err_data)) . PHP_EOL);
    }
    return ['success' => $user_phones, 'fail' => $enc_err_data];
}


function getSuccessPhone($uids)
{
    $success = getPhone($uids)['success'];
    return array_column($success, 'phone', 'uid');
}


function outExcel($data, $filename = '导出数据')
{
    $sheet_names      = array_keys($data);
    $first_sheet_name = $sheet_names[0];
    $first_data       = $data[$first_sheet_name];
    foreach ($first_data as &$datum) {
        if (!is_array($datum)) {
            $datum = [$datum];
        }
    }
    unset($data[$first_sheet_name]);

    $filename = date("Y-m-d") . $filename . '.xlsx';
    $path     = app()->getRuntimePath() . 'excel';
    if (!is_dir($path)) {
        mkdir($path, 0755, true);
    }
    $excel = new \Vtiful\Kernel\Excel(compact('path'));
    $excel->fileName($filename, $first_sheet_name)->data($first_data);

    foreach ($data as $item_sheet_name => $item_sheet_data) {
        foreach ($item_sheet_data as &$item_sheet_data_item) {
            if (!is_array($item_sheet_data_item)) {
                $item_sheet_data_item = [$item_sheet_data_item];
            }
        }
        $excel->addSheet($item_sheet_name)->data($item_sheet_data);
    }

    return $excel->output();
}


/**
 * @方法描述: 读取Excel
 * <AUTHOR>
 * @Date 2022/9/6 10:24
 * @param $filename
 * @return array
 */
function readExcel($filename, $path = '', $set_type = [])
{
    $path = empty($path) ? (app()->getRuntimePath() . 'in' . DIRECTORY_SEPARATOR) : $path;

//        $path   = $path.$filename;
//        $startI = 1;
//        #下载文件
//        $local_path = download_image($path, 'xls');
//        #解析文件
//        $data = getExcelData($local_path, $startI);
//
//        @unlink($local_path);
//        $excelData = $data['data'];
//        unset($excelData[$startI]);
//        return $excelData;

    $config    = ['path' => $path];
    $excel     = new \Vtiful\Kernel\Excel($config);
    $sheetList = $excel->openFile($filename)->sheetList();
    $data      = [
        'path'      => $path,
        'file_name' => $filename,
    ];

    $i = 1;
    foreach ($sheetList as $sheetName) {

        $excel = $excel->openSheet($sheetName);
        if (!empty($set_type)) {
            $excel = $excel->setType($set_type);
        }
        $sheetData = $excel->getSheetData();

        $data['sheet'][$i] = [
            'name' => $sheetName,
            'list' => $sheetData,
        ];

        $data['sheet' . $i] = [
            'name' => $sheetName,
            'list' => $sheetData,
        ];
        $i++;
    }
    return $data;
}

/**
 * 迭代器方法，实现无限分类树
 * @param array $array 原始数据包
 * @return array
 */
function buildTree($array, $children = 'children', $pid = 'pid')
{
    if (empty($array)) {
        return [];
    }
    $map        = array();
    $fotmatTree = array();
    foreach ($array as &$vo) {
        $map[$vo['id']]            = &$vo;
        $map[$vo['id']][$children] = array();
    }
    unset($vo);

    foreach ($array as &$item) {
        $parent = &$map[$item[$pid]];
        if (empty($parent)) {
            $fotmatTree[] = &$item;
        } else {
            $parent[$children][] = &$item;

        }
    }
    unset($map);
    return $fotmatTree;
}

function tree_to_array($tree, $children = 'children', &$list = [])
{
    if (is_array($tree)) {
        foreach ($tree as $key => $value) {
            $reffer = $value;
            unset($reffer[$children]);
            if (!empty($value[$children])) {
                tree_to_array($value[$children], $children, $list);
            }
            $list[] = $reffer;
        }
    }
    return $list;
}

function get_periods_info($periods, $period_fields = "*", $package_fields = "*")
{
    $items    = Es::name(Es::PERIODS)->where([['id', 'in', $periods]])->order(['id' => 'desc'])->field($period_fields)->select()->toArray();
    $packages = Es::name(Es::PERIODS_PACKAGE)->where([
        ['period_id', 'in', $periods],
        ['is_hidden', '=', 0],
    ])->order(['period_id' => 'desc'])->field($package_fields)->select()->toArray();

    $all_packages = [];
    $pids         = [];
    foreach ($packages as $temp_package) {
        $associated_products = json_decode($temp_package['associated_products'], true);
        $pids                = array_merge($pids, array_column($associated_products, 'product_id'));

        $temp_package['associated_products']        = $associated_products;
        $all_packages[$temp_package['period_id']][] = $temp_package;
    }

    $products = Es::name(Es::PRODUCTS)->where([
        ['id', 'in', $pids]
    ])->order(['id' => 'desc'])->select()->toArray();
    $products = array_column($products, null, 'id');

    foreach ($all_packages as $per_id => $periods_package) {
        foreach ($periods_package as $i => $package) {
            foreach ($package['associated_products'] as $j => $package_product_item) {
                $package_product_item['product_info'] = $products[$package_product_item['product_id']] ?? [];
                $package['associated_products'][$j]   = $package_product_item;
            }
            $periods_package[$i] = $package;
        }
        $all_packages[$per_id] = $periods_package;
    }
    foreach ($items as &$item) {
        $item['packages'] = $all_packages[$item['id']] ?? [];
    }
    return $items;
}

//查询出所有白酒和食品分类
function getProductTypes($types = [], $pids = [])
{
    $type_arr = $find_type = [];

    $product_types = Db::connect('mysql_wiki_prod')->name('product_type')->order('fid', 'desc')->column('id,name,fid,pid');
    $product_types = buildTree($product_types, 'children', 'fid');
    $product_types = array_column($product_types, null, 'name');

    foreach ($product_types as $product_type_item) {
        if (in_array($product_type_item['name'], $types) || in_array($product_type_item['pid'], $pids)) {
            $type_arr[$product_type_item['name']] = tree_to_array([$product_types[$product_type_item['name']]]);
            $find_type                            = array_merge($find_type, array_column($type_arr[$product_type_item['name']], 'name'));
        }
    }

    return [$type_arr, $find_type];
}

//获取全部盲盒
function getOrderMysteryBox($types = [], $pids = [])
{
    $order_mystery_box = Db::connect('database_orders_prod')->name('order_mystery_box_log')->column('main_order_no,package_id,product_info', 'main_order_no');
    $mboxs             = [];
    foreach ($order_mystery_box as $box) {
        $mboxs["{$box['main_order_no']}_{$box['package_id']}"] = json_decode($box['product_info'], true);
    }
    return $mboxs;
}

//数组转字符串
function getArrayString($arr)
{
    if (!is_array($arr)) {
        return $arr;
    }
    $stringArr = [];
    foreach ($arr as $k => $v) {
        $is_var = in_array(substr($v, 0, 1), ['$', '_']);
        if (!$is_var) {
            $v = str_replace("'", "\'", $v);
            $k = str_replace("'", "\'", $k);
        }
        $stringArr[] = "'" . $k . "' => " . ($is_var ? $v : "'{$v}'");
    }
    return implode(", ", $stringArr);
}

//获取期数成本
function getPeriodInventorys($period_ids, $field = "*")
{
    $group_list = [];
    $list       = Db::connect('mysql_commodities_prod')->name('periods_product_inventory')
        ->where('period', 'in', $period_ids)
        ->column($field);
    foreach ($list as $item) {
        $group_list[$item['period']][$item['product_id']] = $item;
    }
    return $group_list;
}

//获取套餐成本
function getPackages($package_ids, $field = "*")
{
    $packages = Es::name(Es::PERIODS_PACKAGE)->where([['id', 'in', $package_ids]])->field($field)->select()->toArray();

    $period_ids                = array_values(array_unique(array_column($packages, 'period_id')));
    $period_product_inventorys = getPeriodInventorys($period_ids, 'id,period,product_id,costprice,short_code,product_name,en_product_name'); //期数产品成本

    $pids = [];
    foreach ($period_product_inventorys as $ppi_period_id => $ppi_items) {
        foreach ($ppi_items as $ppi_product_id => $ppi_product_info) {
            $pids[$ppi_product_id] = $ppi_product_id;
        }
    }
    $product_countrys = Db::connect('mysql_wiki_prod')->name('country_base')->column('country_name_cn,country_name_en,id', 'id');
    $product_units = Db::connect('mysql_wiki_prod')->name('product_unit')->column('name', 'id');
    $product_types = Db::connect('mysql_wiki_prod')->name('product_type')->order('fid', 'desc')->column('name', 'id');
    $wiki_products = Db::connect('mysql_wiki_prod')->name('products')->where('id', 'in', array_keys($pids))->column('id,capacity,product_unit,product_type,country_id', 'id');

    foreach ($packages as &$pkg_info) {
        $pkg_costprice         = 0; //套餐成本
        $pkg_nums              = 0; //套餐数量
        $product_costprice     = []; //单瓶成本
        $product_num_costprice = [];//单个产品 多个数量成本
        $product_costrate      = [];//单瓶成本比例
        $product_salesprice    = [];//单瓶售价
        $associated_products   = json_decode($pkg_info['associated_products'], true);

        foreach ($associated_products as &$product) {
            $pi_num   = $product['nums'];
            $pkg_nums += $pi_num;
            if (!is_array($product['product_id'])) {
                $pi_costprice               = $period_product_inventorys[$pkg_info['period_id']][$product['product_id']]['costprice'] ?? 0;
                $product['costprice']       = $pi_costprice;
                $product['short_code']      = $period_product_inventorys[$pkg_info['period_id']][$product['product_id']]['short_code'] ?? '未知简码';
                $product['product_name']    = $period_product_inventorys[$pkg_info['period_id']][$product['product_id']]['product_name'] ?? '';
                $product['en_product_name'] = $period_product_inventorys[$pkg_info['period_id']][$product['product_id']]['en_product_name'] ?? '';
                $product['capacity']        = $wiki_products[$product['product_id']]['capacity'] ?? '';
                $product['product_unit']    = $product_units[($wiki_products[$product['product_id']]['product_unit'] ?? '')] ?? '';
                $product['country_name_cn']    = $product_countrys[($wiki_products[$product['product_id']]['country_id'] ?? '')]['country_name_cn'] ?? '';
                $product['country_name_en']    = $product_countrys[($wiki_products[$product['product_id']]['country_id'] ?? '')]['country_name_en'] ?? '';
                $product['product_type']    = $product_types[($wiki_products[$product['product_id']]['product_type'] ?? '')] ?? '';

                $product_costprice[$product['product_id']] = $pi_costprice;
                if ($pkg_info['is_mystery_box'] == 0) {
                    $product_num_costprice[$product['product_id']] = [
                        'total_costprice' => bcmul($pi_num, $pi_costprice, 2),
                        'costprice'       => $pi_costprice,
                        'nums'            => $pi_num,
                    ];
                    $pkg_costprice                                 = bcadd($pkg_costprice, bcmul($pi_num, $pi_costprice, 2), 2);
                }
            }
        }
        foreach ($product_num_costprice as &$pnc) {
            if ($pkg_costprice == 0) {
                $pnc['total_rate'] = 0;
            } else {
                $pnc['total_rate'] = bcdiv($pnc['total_costprice'], $pkg_costprice, 8);
            }
            if ($pnc['nums'] == 0) {
                $pnc['rate'] = 0;
            } else {
                $pnc['rate'] = bcdiv($pnc['total_rate'], $pnc['nums'], 8);
            }
            $pnc['sales_price']       = bcmul($pnc['rate'], $pkg_info['price'], 2);
            $pnc['total_sales_price'] = bcmul($pnc['total_rate'], $pkg_info['price'], 2);
        }
        $pkg_info['associated_products']   = $associated_products;
        $pkg_info['costprice']             = $pkg_costprice;
        $pkg_info['pkg_nums']              = $pkg_nums;
        $pkg_info['product_costprice']     = $product_costprice;
        $pkg_info['product_num_costprice'] = $product_num_costprice;
    }
    $packages = array_column($packages, null, 'id');

    return $packages;
}

function get_age_by_id_card($idcard, $isExact = true)
{
    if (empty($idcard)) return null;
    // 从身份证中获取出生日期
    $birthDate = strtotime(substr($idcard, 6, 8)); //截取日期并转为时间戳

    // 格式化[出生日期]
    $year  = date('Y', $birthDate); //yyyy
    $month = date('m', $birthDate); //mm
    $day   = date('d', $birthDate); //dd

    // 格式化[当前日期]
    $currentYear  = date('Y'); //yyyy
    $currentMonth = date('m'); //mm
    $currentDay   = date('d'); //dd

    // 计算年龄()
    $age = $currentYear - $year; //今年减去生日年
    if ($isExact) {
        // 如果出生月大于当前月或出生月等于当前月但出生日大于当前日则减一岁
        if ($month > $currentMonth || ($month == $currentMonth && $day > $currentDay)) { //深层判断(日)
            $age--;
        }
    }

    return $age;
}


/**
 * @方法描述:列表数据分组
 * <AUTHOR>
 * @Date 2023/4/19 14:04
 * @param $array
 * @param $field
 * @return array
 */
if (!function_exists('array_group')) {
    function array_group($array, $field)
    {
        $gropu_list = [];
        foreach ($array as $item) {
            $gropu_list[$item[$field]][] = $item;
        }
        return $gropu_list;
    }
}



// todo  .... 计算简码成本比例
//region计算套餐成本
//$product_inventory = Db::connect('mysql_commodities')->name('periods_product_inventory')
//    ->where('period', '=', $order['period'])
//    ->column('*');
//$product_inventory = array_column($product_inventory, null, 'product_id');
//$package           = Es::name(Es::PERIODS_PACKAGE)->where([['id', '==', $order['package_id']]])->find();
//if ($package['is_mystery_box'] == 0) {
//    $associated_products = json_decode($package['associated_products'], true);
//} else {
//    $associated_products = json_decode(Db::connect('mysql_orders')->name('order_mystery_box_log')
//        ->where('main_order_no', $order['main_order_no'])
//        ->where('package_id', $order['package_id'])
//        ->value('product_info'), true);
//}
//
//$pkg_costprice = 0;
//foreach ($associated_products as &$product) {
//    $pi_num                    = $product['nums'];
//    $p_inventory_info          = $product_inventory[$product['product_id']];
//    $product['unit_costprice'] = $p_inventory_info['costprice'];//单个产品单个数量成本
//    $product['costprice']      = bcmul($pi_num, $p_inventory_info['costprice'], 2); //单个产品多个数量成本
//    $product['short_code']     = $p_inventory_info['short_code'];
//    $product['bar_code']       = $p_inventory_info['bar_code'];
//    $product['product_name']   = $p_inventory_info['product_name'];
//    $pkg_costprice             = bcadd($pkg_costprice, $product['costprice'], 2); //套餐成本
//}
//foreach ($associated_products as &$product) {
//    $product['unit_rate'] = bcdiv($product['unit_costprice'], $pkg_costprice, 6);//单个产品单个数量成本比例
//    $product['rate']      = bcdiv($product['costprice'], $pkg_costprice, 6);//单个产品多个数量成本比例
//}
//$associated_products = array_column($associated_products, null, 'short_code');
//$wiki_products       = Es::name(Es::PRODUCTS)->where([['short_code', 'in', array_keys($associated_products)]])->field('short_code,product_unit_name,canning_years,capacity,tax_rate,is_gift')->select()->toArray();
//$wiki_products       = array_column($wiki_products, null, 'short_code');
//endregion计算套餐成本
