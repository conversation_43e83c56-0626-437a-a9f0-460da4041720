<?php

namespace app\service\v3;

use app\BaseService;
use app\model\Work;
use app\validate\WorkValidate;
use think\facade\Db;

/**
 * 工单
 * Class WorkService
 * @package app\service\v3
 */
class WorkService extends BaseService
{
    public function __construct()
    {
        $this->model       = new Work;
        $this->validate    = WorkValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2023/06/26 15:22
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id id
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //id
            }
            #endregion

        };
    }


    public function waybillsKuaidi100($param)
    {
        $data = Db::connect('mysql_customer_service_prod')->name('waybills')
            ->where('waybill', 'like', "%JD%")
            ->where('status', 0)
            ->column('id,waybill,expressType');

        $waybill_ids = [];
        foreach ($data as $datum) {
            $waybill_ids[$datum['waybill']]['ids'][]       = $datum['id'];
            $waybill_ids[$datum['waybill']]['expressType'] = $datum['expressType'];
        }

        $done_ids = [];

        foreach ($waybill_ids as $waybill => $item) {
            $waybill_info = \Curl::kuaidi100MapTrack([
                'logisticCode' => $waybill,
                'expressType'  => $item['expressType'],
            ]);
            if (!empty($waybill_info)) {

                if ($waybill_info['state'] == 3) {
                    $done_ids = array_merge($done_ids, $item['ids']);
                }
            }
        }

        $nums = Db::connect('mysql_customer_service_prod')->name('waybills')
            ->where('id','in', $done_ids)
            ->update(['status'=>1]);


        return json_encode([$nums,
            count($done_ids),
            $done_ids,]);


    }

}



